# API Integration Documentation

## Overview

This Buddha Quotes Instagram Bot has been updated to use a combination of **free APIs** and **RapidAPI** services following best practices and official documentation.

## API Strategy

The bot uses a **tiered approach** for maximum reliability:

1. **Primary**: Free APIs (no authentication required)
2. **Secondary**: Free APIs with simple authentication
3. **Fallback**: RapidAPI services (requires subscription)
4. **Ultimate Fallback**: Curated content

## Implemented APIs

### 1. Quote Generation

#### Primary: ZenQuotes API (Free)
- **URL**: `https://zenquotes.io/api/random`
- **Authentication**: None required
- **Rate Limit**: Generous free tier
- **Implementation**: `tools/content_tools.py` - QuoteGeneratorTool

#### Secondary: Quotable.io API (Free)
- **URL**: `https://api.quotable.io/random`
- **Authentication**: None required
- **Features**: Tag filtering, length limits
- **Implementation**: Backup in QuoteGeneratorTool

#### Fallback: RapidAPI Quotes
- **URL**: `quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com`
- **Authentication**: RapidAPI key required
- **Implementation**: Third option in QuoteGeneratorTool

### 2. Trending Topics Research

#### Primary: Reddit API (Free)
- **URL**: `https://www.reddit.com/r/{subreddit}/hot.json`
- **Authentication**: None (public endpoints)
- **Subreddits**: meditation, mindfulness, spirituality, getmotivated
- **Implementation**: `tools/content_tools.py` - TrendResearchTool

#### Secondary: HackerNews API (Free)
- **URL**: `https://hacker-news.firebaseio.com/v0/topstories.json`
- **Authentication**: None required
- **Focus**: Tech/productivity trends
- **Implementation**: Backup in TrendResearchTool

#### Optional: NewsAPI
- **URL**: `https://newsapi.org/v2/top-headlines`
- **Authentication**: API key (free tier available)
- **Implementation**: If NEWSAPI_KEY is configured

### 3. Social Media Trends

#### Primary: Reddit Communities
- **Sources**: r/meditation, r/mindfulness, r/spirituality
- **Method**: Extract trending terms from post titles
- **Implementation**: `tools/content_tools.py` - SocialMediaTrendsTool

#### Secondary: News Analysis
- **Source**: NewsAPI wellness articles
- **Method**: Extract trending wellness terms
- **Implementation**: Backup in SocialMediaTrendsTool

#### Fallback: Curated Hashtags
- **Method**: Platform-specific curated hashtag lists
- **Platforms**: Instagram, Twitter, TikTok
- **Implementation**: Ultimate fallback

## Environment Variables

```bash
# Required for OpenAI/CrewAI
OPENAI_API_KEY=your_openai_api_key

# Optional: For enhanced quote generation
NEWSAPI_KEY=your_newsapi_key_here

# Optional: RapidAPI fallback
RAPIDAPI_KEY=your_rapidapi_key
GOOGLE_TRENDS_API_KEY=your_rapidapi_key  # Legacy support

# Instagram credentials
INSTA_USERNAME=your_instagram_username
INSTA_PASSWORD=your_instagram_password

# ElevenLabs for audio (if used)
ELEVENLABS_API_KEY=your_elevenlabs_key
```

## RapidAPI Integration

### Following RapidAPI Documentation

The implementation follows RapidAPI best practices:

1. **Headers**: Proper X-RapidAPI-Key and X-RapidAPI-Host headers
2. **Error Handling**: Graceful fallback when APIs are not subscribed
3. **Rate Limiting**: Timeout controls and retry logic
4. **Documentation**: Clear API endpoint documentation

### Recommended RapidAPI Services

If you want to subscribe to RapidAPI services for enhanced functionality:

1. **Google Trends API**: `google-trends9.p.rapidapi.com`
2. **Quotes API**: `quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com`
3. **Twitter Trends**: `twitter-trends8.p.rapidapi.com`

### RapidAPI Setup

1. Sign up at [RapidAPI.com](https://rapidapi.com)
2. Subscribe to desired APIs
3. Get your API key from the dashboard
4. Add to `.env` file as `RAPIDAPI_KEY`

## Free API Alternatives

### Getting NewsAPI Key (Free)

1. Visit [NewsAPI.org](https://newsapi.org)
2. Sign up for free account
3. Get API key (free tier: 1000 requests/day)
4. Add to `.env` as `NEWSAPI_KEY`

### No Authentication Required

These APIs work without any setup:
- ZenQuotes.io
- Reddit public endpoints
- HackerNews API

## Testing

Run the test script to verify API integration:

```bash
python test_rapidapi.py
```

This will test:
- All tool implementations
- Direct API endpoints
- Fallback mechanisms
- Error handling

## Error Handling

The implementation includes robust error handling:

1. **Network Timeouts**: 10-second timeouts for all requests
2. **API Failures**: Automatic fallback to next API
3. **Rate Limiting**: Graceful handling of rate limit errors
4. **SSL Issues**: Bypass for problematic certificates
5. **Ultimate Fallback**: Curated content when all APIs fail

## Performance Optimization

- **Parallel Requests**: Multiple APIs called efficiently
- **Caching**: Results cached where appropriate
- **Minimal Requests**: Only essential API calls made
- **Timeout Controls**: Prevent hanging requests

## Monitoring and Logging

The bot logs all API interactions:
- Successful API calls
- Failed requests with error details
- Fallback activations
- Performance metrics

Check `logs/buddha_bot.log` for detailed API interaction logs.

## Future Enhancements

Potential improvements:
1. **Caching Layer**: Redis for API response caching
2. **Analytics**: Track API usage and success rates
3. **A/B Testing**: Compare different API sources
4. **Custom APIs**: Build proprietary trend detection
5. **Machine Learning**: Enhance content relevance

## Support

For API-related issues:
1. Check the test script output
2. Review logs for error details
3. Verify environment variables
4. Test individual APIs manually
5. Check API service status pages
