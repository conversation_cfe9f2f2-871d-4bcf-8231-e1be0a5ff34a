# Buddha Quotes Instagram Bot - Deployment Guide

## 🎯 **Environment Modes**

The Buddha Quotes Instagram Bot supports two distinct execution modes:

### 🏠 **Local Development Mode**
- **Purpose**: Testing, development, and one-time content generation
- **Behavior**: Executes content pipeline **once** and **exits**
- **Use Cases**: 
  - Testing new features
  - Generating content for manual review
  - Development and debugging
  - One-time content creation

### 🏭 **Production Mode**
- **Purpose**: Automated, scheduled social media posting
- **Behavior**: Runs **continuously** with scheduled posts
- **Use Cases**:
  - Automated Instagram posting
  - Server deployment
  - Continuous content generation
  - Social media automation

---

## 🚀 **Running the Bot**

### **Method 1: Using Dedicated Scripts (Recommended)**

#### **Local Mode:**
```bash
python run_local.py
```
- ✅ Forces local development mode
- ✅ Executes once and exits
- ✅ Perfect for testing

#### **Production Mode:**
```bash
python run_production.py
```
- ✅ Forces production mode with scheduling
- ✅ Runs continuously
- ✅ Scheduled posts at 8 AM, 2 PM, 8 PM IST

### **Method 2: Using Environment Variable**

#### **Local Mode:**
```bash
# Set environment variable
export ENVIRONMENT=development  # Linux/Mac
set ENVIRONMENT=development     # Windows

# Run the bot
python main.py
```

#### **Production Mode:**
```bash
# Set environment variable
export ENVIRONMENT=production   # Linux/Mac
set ENVIRONMENT=production      # Windows

# Run the bot
python main.py
```

### **Method 3: Using .env File**

Edit your `.env` file:

```bash
# For Local Development
ENVIRONMENT=development

# For Production
ENVIRONMENT=production
```

Then run:
```bash
python main.py
```

---

## 📋 **Execution Behavior**

### **🏠 Local Development Mode Flow:**
1. **System Verification**: Checks all components
2. **Immediate Execution**: Runs content pipeline once
3. **Content Generation**: Creates quote, image, voice, video
4. **Exit**: Completes and exits (no scheduling)
5. **Output**: Generated files in `temp/` and `assets/` directories

**Example Output:**
```
🏠 LOCAL DEVELOPMENT MODE
==================================================
   📍 Running content pipeline immediately...
   🔄 Will execute once and exit (no scheduling)

🤖 Using openai LLM for CrewAI execution
⚙️  Executing CrewAI pipeline with fallback support...
✅ Execution completed successfully with openai
🎉 Local execution completed successfully!
   📊 Content generated and ready for use
🏁 Local mode complete - exiting...
```

### **🏭 Production Mode Flow:**
1. **System Verification**: Checks all components
2. **Initial Test**: Runs verification test
3. **Schedule Setup**: Configures posting times
4. **Continuous Loop**: Runs indefinitely with scheduled posts
5. **Auto-Retry**: Handles failures and retries

**Example Output:**
```
🏭 PRODUCTION MODE
==================================================
🧪 Running initial system verification...
📅 PRODUCTION SCHEDULING MODE ACTIVATED
⏰ Scheduled posts for:
   • 8:00 AM IST
   • 2:00 PM IST
   • 8:00 PM IST
🔄 Starting scheduler loop...
   💡 Press Ctrl+C to stop the bot
```

---

## ⚙️ **Configuration**

### **Environment Detection**

The bot automatically detects the environment based on:

1. **ENVIRONMENT** variable in `.env` file
2. **ENVIRONMENT** system environment variable
3. **Production indicators**:
   - `PROD=true`
   - Docker container (`/.dockerenv`)
   - Railway deployment (`RAILWAY_ENVIRONMENT`)
   - Heroku deployment (`HEROKU_APP_NAME`)
   - Command line flag (`--production`)

### **Priority Order:**
1. System environment variables (highest priority)
2. `.env` file variables
3. Auto-detection based on deployment platform
4. Default to `development` mode

---

## 🛠️ **Development Workflow**

### **Local Testing:**
```bash
# 1. Test the bot locally
python run_local.py

# 2. Check generated content
ls temp/          # Videos, audio files
ls assets/        # Background images
ls logs/          # Execution logs

# 3. Review logs for any issues
tail -f logs/buddha_bot.log
```

### **Production Deployment:**
```bash
# 1. Set production environment
echo "ENVIRONMENT=production" >> .env

# 2. Deploy to server
python run_production.py

# 3. Monitor logs
tail -f logs/buddha_bot.log
```

---

## 📊 **Monitoring & Logs**

### **Log Files:**
- **`logs/buddha_bot.log`**: Main application logs
- **`logs/fallback_test.log`**: LLM fallback testing logs

### **Generated Content:**
- **`temp/`**: Temporary files (videos, audio, text overlays)
- **`assets/backgrounds/`**: Generated background images
- **`assets/`**: Other media assets

### **Key Log Indicators:**

#### **Successful Local Execution:**
```
🎉 Local execution completed successfully!
📊 Content generated and ready for use
🏁 Local mode complete - exiting...
```

#### **Successful Production Scheduling:**
```
📅 PRODUCTION SCHEDULING MODE ACTIVATED
⏰ Scheduled posts for: 8:00 AM, 2:00 PM, 8:00 PM IST
🔄 Starting scheduler loop...
```

#### **LLM Fallback Activation:**
```
⏰ Rate limit detected for openai: Rate limit reached...
🔄 Switched from openai to deepseek
🔄 Reconfigured 7 agents with deepseek LLM
✅ Execution completed successfully with deepseek
```

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **Bot Exits Immediately in Production:**
- **Cause**: Environment set to `development`
- **Solution**: Set `ENVIRONMENT=production` in `.env` or use `run_production.py`

#### **Bot Runs Continuously in Local Mode:**
- **Cause**: Environment set to `production`
- **Solution**: Set `ENVIRONMENT=development` in `.env` or use `run_local.py`

#### **Critical Component Failures:**
- **Cause**: Missing API keys or dependencies
- **Solution**: Check `.env` configuration and install required packages

#### **Rate Limit Errors:**
- **Cause**: OpenAI API rate limits exceeded
- **Solution**: Fallback to DeepSeek should activate automatically

### **Emergency Stops:**

#### **Local Mode:**
- Execution completes automatically
- Use `Ctrl+C` if needed

#### **Production Mode:**
- Use `Ctrl+C` to stop gracefully
- Bot will log shutdown and exit cleanly

---

## 🎯 **Best Practices**

### **Development:**
1. **Always test locally first** using `python run_local.py`
2. **Review generated content** before production deployment
3. **Check logs** for any warnings or errors
4. **Verify API keys** are working correctly

### **Production:**
1. **Use dedicated server** or cloud platform
2. **Monitor logs regularly** for issues
3. **Set up log rotation** to prevent disk space issues
4. **Configure alerts** for critical failures
5. **Keep API keys secure** and rotate regularly

### **Deployment:**
1. **Use environment-specific configurations**
2. **Test fallback mechanisms** before going live
3. **Set up monitoring** and alerting
4. **Plan for graceful shutdowns** and restarts

---

## 📈 **Scaling Considerations**

### **Single Instance (Current):**
- ✅ Perfect for personal/small business use
- ✅ 3 posts per day (8 AM, 2 PM, 8 PM IST)
- ✅ Automatic fallback and error handling

### **Multiple Instances:**
- 🔄 Deploy multiple bots for different accounts
- 🔄 Use different scheduling times
- 🔄 Separate API keys for rate limit distribution

### **Enterprise Deployment:**
- 🏢 Container orchestration (Docker/Kubernetes)
- 🏢 Load balancing and high availability
- 🏢 Centralized logging and monitoring
- 🏢 Database integration for content management

The Buddha Quotes Instagram Bot is now fully configured for both local development and production deployment with clear separation of concerns and robust error handling!
