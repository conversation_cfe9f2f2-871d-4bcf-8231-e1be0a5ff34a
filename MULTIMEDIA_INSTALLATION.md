
# Multimedia Agents Installation Guide

## Required Dependencies

Install the following packages for full multimedia functionality:

```bash
# Core video processing
pip install moviepy opencv-python

# Image processing and utilities  
pip install pillow numpy requests

# CrewAI and AI framework
pip install crewai pydantic

# Optional: For advanced audio processing
pip install librosa soundfile
```

## API Keys Setup

Add the following to your .env file:

```bash
# Required for voice synthesis
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Required for AI agents
OPENAI_API_KEY=your_openai_api_key

# Optional: For enhanced functionality
MIDJOURNEY_API_KEY=your_midjourney_api_key
PIXABAY_API_KEY=your_pixabay_api_key
```

## Getting API Keys

1. **ElevenLabs**: Sign up at https://elevenlabs.io
2. **OpenAI**: Get API key from https://platform.openai.com
3. **Pixabay**: Free API at https://pixabay.com/api/docs/
4. **Midjourney**: Currently requires Discord bot setup

## Troubleshooting

- If <PERSON><PERSON><PERSON> fails, try: `pip install moviepy[optional]`
- For OpenCV issues on Windows: `pip install opencv-python-headless`
- For audio issues: Install system audio codecs (ffmpeg)

## Testing

Run this test script to verify installation:
```bash
python test_multimedia_agents.py
```
