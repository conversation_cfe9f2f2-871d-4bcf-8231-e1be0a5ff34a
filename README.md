# Buddha Quotes Instagram Bot with CrewAI

An autonomous multi-agent system built with CrewAI that creates and posts Buddha-inspired motivational content to Instagram.

## 🤖 CrewAI Agents

- **Content Researcher**: Analyzes trending topics using Google Trends
- **Quote Creator**: Generates authentic Buddha-style quotes
- **Video Producer**: Creates engaging video content
- **Social Media Manager**: Posts content to Instagram with optimal hashtags

## 🚀 Quick Start

1. **Setup Project**
   ```bash
   python setup.py
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

3. **Run the Bot**
   ```bash
   python main.py
   ```

## 🛠️ CrewAI Tools Used

- **TrendResearchTool**: Google Trends integration
- **QuoteGeneratorTool**: Buddha-style quote generation
- **VideoCreatorTool**: Video creation with MoviePy
- **InstagramPostTool**: Instagram posting automation

## 📅 Scheduling

Posts automatically at:
- 8:00 AM IST
- 2:00 PM IST  
- 8:00 PM IST

## 🐳 Docker Deployment

```bash
docker build -t buddha-bot .
docker run -d --env-file .env buddha-bot
```

## 🔧 Configuration

Update `config/settings.py` for custom configurations and `tools/` directory for custom CrewAI tools.