from crewai import Agent, Task, Crew, Process
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
from tools.video_tools import VideoCreatorTool
from tools.multimedia_tools import MidjourneyImageTool, ElevenLabsVoiceTool, PixabayMusicTool
from tools.instagram_tools import InstagramPostTool
import os
import logging

# Import LLM fallback support
try:
    from utils.crewai_config_manager import CrewAIConfigManager
    FALLBACK_AVAILABLE = True
except ImportError:
    FALLBACK_AVAILABLE = False
    print("Warning: LLM fallback not available")

# Initialize tools
trend_tool = TrendResearchTool()
quote_tool = QuoteGeneratorTool()
social_trends_tool = SocialMediaTrendsTool()
video_tool = VideoCreatorTool()
midjourney_tool = MidjourneyImageTool()
voiceover_tool = ElevenLabsVoiceTool()
music_tool = PixabayMusicTool()
instagram_tool = InstagramPostTool()

# Define Agents
content_researcher = Agent(
    role='Content Researcher',
    goal='Find trending spiritual topics and hashtags',
    backstory='Expert researcher specializing in mindfulness and spiritual trends.',
    tools=[trend_tool, social_trends_tool],
    verbose=True
)

quote_creator = Agent(
    role='Quote Creator',
    goal='Write original Buddha-style motivational quotes',
    backstory='Philosophical content creator with Buddhist insight.',
    tools=[quote_tool],
    verbose=True
)

prompt_engineer = Agent(
    role='Prompt Engineer',
    goal='Generate optimized MidJourney prompts for visuals',
    backstory='MidJourney expert who crafts detailed prompts for spiritual images.',
    tools=[midjourney_tool],
    verbose=True
)

voiceover_producer = Agent(
    role='Voiceover Artist',
    goal='Create voiceovers using Grandpa Spuds Oxley style from Eleven Labs',
    backstory='Voice artist trained in warm, wise-sounding vocal tones.',
    tools=[voiceover_tool],
    verbose=True
)

music_selector = Agent(
    role='Music Selector',
    goal='Find soft spiritual background music for videos',
    backstory='Expert in matching emotional tone with royalty-free audio.',
    tools=[music_tool],
    verbose=True
)

video_producer = Agent(
    role='Video Producer',
    goal='Create cinematic video with effects, image, voiceover, and music',
    backstory='Professional video editor focused on inspirational content.',
    tools=[video_tool],
    verbose=True
)

social_media_manager = Agent(
    role='Social Media Manager',
    goal='Post high-performing spiritual content to Instagram',
    backstory='Expert in caption writing, hashtag optimization, and Reels posting.',
    tools=[instagram_tool],
    verbose=True
)

class BuddhaQuoteCrew:
    def __init__(self):
        self.crew = None
        self.logger = logging.getLogger(__name__)

        # Initialize CrewAI config manager if available
        if FALLBACK_AVAILABLE:
            self.config_manager = CrewAIConfigManager(self.logger)
        else:
            self.config_manager = None
            self.logger.warning("⚠️  LLM fallback not available - using default configuration")

    def _create_fallback_enabled_agents(self):
        """Create agents with fallback-enabled LLM configuration"""
        agents = []

        # Content Researcher
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Content Researcher',
            goal='Find trending spiritual topics and hashtags',
            backstory='Expert researcher specializing in mindfulness and spiritual trends.',
            tools=[trend_tool, social_trends_tool],
            verbose=False,  # Reduce verbosity for rate limiting
            max_iter=2
        ))

        # Quote Creator
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Quote Creator',
            goal='Write original Buddha-style motivational quotes',
            backstory='Philosophical content creator with Buddhist insight.',
            tools=[quote_tool],
            verbose=False,
            max_iter=2
        ))

        # Prompt Engineer
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Prompt Engineer',
            goal='Generate optimized MidJourney prompts for visuals',
            backstory='MidJourney expert who crafts detailed prompts for spiritual images.',
            tools=[midjourney_tool],
            verbose=False,
            max_iter=2
        ))

        # Voiceover Producer
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Voiceover Artist',
            goal='Create voiceovers using Grandpa Spuds Oxley style from Eleven Labs',
            backstory='Voice artist trained in warm, wise-sounding vocal tones.',
            tools=[voiceover_tool],
            verbose=False,
            max_iter=2
        ))

        # Music Selector
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Music Selector',
            goal='Find soft spiritual background music for videos',
            backstory='Expert in matching emotional tone with royalty-free audio.',
            tools=[music_tool],
            verbose=False,
            max_iter=2
        ))

        # Video Producer
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Video Producer',
            goal='Create cinematic video with effects, image, voiceover, and music',
            backstory='Professional video editor focused on inspirational content.',
            tools=[video_tool],
            verbose=False,
            max_iter=2
        ))

        # Social Media Manager
        agents.append(self.config_manager.create_agent_with_current_llm(
            role='Social Media Manager',
            goal='Post high-performing spiritual content to Instagram',
            backstory='Expert in caption writing, hashtag optimization, and Reels posting.',
            tools=[instagram_tool],
            verbose=False,
            max_iter=2
        ))

        return [agent for agent in agents if agent is not None]

    def create_crew(self):
        """Create crew with fallback-enabled agents"""

        # Create agents with fallback support if available
        if self.config_manager:
            # Use fallback-enabled agent creation
            agents = self._create_fallback_enabled_agents()
        else:
            # Use default agents
            agents = [content_researcher, quote_creator, prompt_engineer, voiceover_producer,
                     music_selector, video_producer, social_media_manager]

        # Define Tasks using the agents list
        research_task = Task(
            description="""
            Research trending topics and hashtags related to Buddha quotes,
            mindfulness, motivation, and spiritual growth. Include 3–5 themes,
            5–10 hashtags, trending keywords, and Reel-friendly audio styles.
            """,
            agent=agents[0],  # Content Researcher
            expected_output="Trending topics + hashtags report"
        )

        quote_task = Task(
            description="""
            Create a Buddha-style motivational quote based on the trending themes.
            Keep it under 150 characters and focused on inner peace or resilience.
            """,
            agent=agents[1],  # Quote Creator
            expected_output="A short Buddha-style quote"
        )

        prompt_task = Task(
            description="""
            Generate a high-quality prompt for MidJourney to create a vertical image
            with a sunrise mountain scene, Buddha silhouette, soft lighting, cinematic tone.
            Include keywords for composition and color grading.
            """,
            agent=agents[2],  # Prompt Engineer
            expected_output="MidJourney image prompt"
        )

        voiceover_task = Task(
            description="""
            Generate a spoken voiceover using the quote. Use Eleven Labs with
            Grandpa Spuds Oxley voice, save to audio file.
            """,
            agent=agents[3],  # Voiceover Producer
            expected_output="Path to voiceover file"
        )

        music_task = Task(
            description="""
            Search pixabay.com for gentle, ambient royalty-free music that fits the tone
            of the quote and video. Provide download link or audio path.
            """,
            agent=agents[4],  # Music Selector
            expected_output="Path to background music file"
        )

        video_task = Task(
            description="""
            Combine the generated image, voiceover, and music into a vertical 9:16
            cinematic video with fade-in/fade-out effects, keeping the video under 15 seconds.
            Use OpenCV, MoviePy, and apply soft transitions and subtitles.
            """,
            agent=agents[5],  # Video Producer
            expected_output="Path to final video file"
        )

        posting_task = Task(
            description="""
            Write an Instagram caption and post the video using high-engagement hashtags.
            Keep caption emotionally reflective and relevant.
            """,
            agent=agents[6],  # Social Media Manager
            expected_output="Confirmation of Instagram post"
        )

        # Create crew with fallback support if available
        if self.config_manager:
            self.crew = self.config_manager.create_crew_with_fallback_support(
                agents=agents,
                tasks=[research_task, quote_task, prompt_task, voiceover_task,
                      music_task, video_task, posting_task],
                process=Process.sequential,
                verbose=False,  # Reduce verbosity for rate limiting
                memory=False,   # Disable memory to reduce API calls
                planning=False  # Disable planning to reduce API calls
            )
        else:
            # Fallback to default crew creation
            self.crew = Crew(
                agents=agents,
                tasks=[research_task, quote_task, prompt_task, voiceover_task,
                      music_task, video_task, posting_task],
                process=Process.sequential,
                verbose=False,  # Reduce verbosity for rate limiting
                memory=False,   # Disable memory to reduce API calls
                planning=False  # Disable planning to reduce API calls
            )

        return self.crew

    def run_pipeline(self):
        if not self.crew:
            self.create_crew()

        result = self.crew.kickoff()
        return result
