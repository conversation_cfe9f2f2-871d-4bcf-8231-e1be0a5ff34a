from crewai import Agent, Task, Crew, Process
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
from tools.video_tools import VideoCreatorTool
from tools.instagram_tools import InstagramPostTool
import os

# Initialize tools
trend_tool = TrendResearchTool()
quote_tool = QuoteGeneratorTool()
social_trends_tool = SocialMediaTrendsTool()
video_tool = VideoCreatorTool()
instagram_tool = InstagramPostTool()

# Define Agents
content_researcher = Agent(
    role='Content Researcher',
    goal='Research trending topics and hashtags related to Buddha quotes and mindfulness',
    backstory="""You are an expert content researcher specializing in spiritual and
    motivational content. You analyze trends from Google Trends and social media platforms
    to find the most engaging topics and hashtags for spiritual content.""",
    tools=[trend_tool, social_trends_tool],
    verbose=True
)

quote_creator = Agent(
    role='Quote Creator',
    goal='Generate authentic Buddha-style motivational quotes',
    backstory="""You are a spiritual content creator with deep knowledge of Buddhist
    philosophy. You create meaningful quotes that inspire peace and mindfulness.""",
    tools=[quote_tool],
    verbose=True
)

video_producer = Agent(
    role='Video Producer',
    goal='Create engaging video content from quotes',
    backstory="""You are a skilled video producer specializing in spiritual content.
    You create visually appealing videos that complement motivational quotes.""",
    tools=[video_tool],
    verbose=True
)

social_media_manager = Agent(
    role='Social Media Manager',
    goal='Post content to Instagram with optimal engagement',
    backstory="""You are a social media expert who knows how to maximize engagement
    on Instagram through strategic posting and hashtag usage.""",
    tools=[instagram_tool],
    verbose=True
)

class BuddhaQuoteCrew:
    def __init__(self):
        self.crew = None

    def create_crew(self):
        # Define Tasks
        research_task = Task(
            description="""Research current trending topics and hashtags related to Buddha quotes,
            mindfulness, motivation, and spiritual growth. Use both Google Trends and social media
            trends to find topics that would resonate with Instagram audiences. Include relevant
            hashtags that are currently trending.""",
            agent=content_researcher,
            expected_output="A comprehensive report with 3-5 trending topics and 5-10 relevant hashtags for spiritual content"
        )

        quote_task = Task(
            description="""Based on the trending topics, generate an original Buddha-style
            motivational quote. The quote should be under 150 characters and encourage
            peace and mindfulness.""",
            agent=quote_creator,
            expected_output="A meaningful Buddha-style quote under 150 characters"
        )

        video_task = Task(
            description="""Create an engaging video using the generated quote. The video
            should be in vertical format suitable for Instagram Reels, with clear text
            overlay and appropriate background.""",
            agent=video_producer,
            expected_output="Path to the created video file"
        )

        posting_task = Task(
            description="""Post the created video to Instagram with an engaging caption
            and relevant hashtags to maximize reach and engagement.""",
            agent=social_media_manager,
            expected_output="Confirmation of successful Instagram post"
        )

        # Create Crew
        self.crew = Crew(
            agents=[content_researcher, quote_creator, video_producer, social_media_manager],
            tasks=[research_task, quote_task, video_task, posting_task],
            process=Process.sequential,
            verbose=True
        )

        return self.crew

    def run_pipeline(self):
        if not self.crew:
            self.create_crew()

        result = self.crew.kickoff()
        return result