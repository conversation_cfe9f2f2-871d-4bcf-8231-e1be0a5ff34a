from crewai import Agent, Task, Crew, Process
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
from tools.video_tools import VideoC<PERSON>Tool, MidJourneyPromptTool, VoiceoverTool, MusicDownloaderTool, CinematicVideoTool
from tools.instagram_tools import InstagramPostTool
import os

# Initialize tools
trend_tool = TrendResearchTool()
quote_tool = QuoteGeneratorTool()
social_trends_tool = SocialMediaTrendsTool()
video_tool = VideoCreatorTool()
midjourney_tool = MidJourneyPromptTool()
voiceover_tool = VoiceoverTool()
music_tool = MusicDownloaderTool()
cinematic_tool = CinematicVideoTool()
instagram_tool = InstagramPostTool()

# Define Agents
content_researcher = Agent(
    role='Content Researcher',
    goal='Find trending spiritual topics and hashtags',
    backstory='Expert researcher specializing in mindfulness and spiritual trends.',
    tools=[trend_tool, social_trends_tool],
    verbose=True
)

quote_creator = Agent(
    role='Quote Creator',
    goal='Write original Buddha-style motivational quotes',
    backstory='Philosophical content creator with Buddhist insight.',
    tools=[quote_tool],
    verbose=True
)

prompt_engineer = Agent(
    role='Prompt Engineer',
    goal='Generate optimized MidJourney prompts for visuals',
    backstory='MidJourney expert who crafts detailed prompts for spiritual images.',
    tools=[midjourney_tool],
    verbose=True
)

voiceover_producer = Agent(
    role='Voiceover Artist',
    goal='Create voiceovers using Grandpa Spuds Oxley style from Eleven Labs',
    backstory='Voice artist trained in warm, wise-sounding vocal tones.',
    tools=[voiceover_tool],
    verbose=True
)

music_selector = Agent(
    role='Music Selector',
    goal='Find soft spiritual background music for videos',
    backstory='Expert in matching emotional tone with royalty-free audio.',
    tools=[music_tool],
    verbose=True
)

video_producer = Agent(
    role='Video Producer',
    goal='Create cinematic video with effects, image, voiceover, and music',
    backstory='Professional video editor focused on inspirational content.',
    tools=[cinematic_tool],
    verbose=True
)

social_media_manager = Agent(
    role='Social Media Manager',
    goal='Post high-performing spiritual content to Instagram',
    backstory='Expert in caption writing, hashtag optimization, and Reels posting.',
    tools=[instagram_tool],
    verbose=True
)

class BuddhaQuoteCrew:
    def __init__(self):
        self.crew = None

    def create_crew(self):
        # Define Tasks
        research_task = Task(
            description="""
            Research trending topics and hashtags related to Buddha quotes,
            mindfulness, motivation, and spiritual growth. Include 3–5 themes,
            5–10 hashtags, trending keywords, and Reel-friendly audio styles.
            """,
            agent=content_researcher,
            expected_output="Trending topics + hashtags report"
        )

        quote_task = Task(
            description="""
            Create a Buddha-style motivational quote based on the trending themes.
            Keep it under 150 characters and focused on inner peace or resilience.
            """,
            agent=quote_creator,
            expected_output="A short Buddha-style quote"
        )

        prompt_task = Task(
            description="""
            Generate a high-quality prompt for MidJourney to create a vertical image
            with a sunrise mountain scene, Buddha silhouette, soft lighting, cinematic tone.
            Include keywords for composition and color grading.
            """,
            agent=prompt_engineer,
            expected_output="MidJourney image prompt"
        )

        voiceover_task = Task(
            description="""
            Generate a spoken voiceover using the quote. Use Eleven Labs with
            Grandpa Spuds Oxley voice, save to audio file.
            """,
            agent=voiceover_producer,
            expected_output="Path to voiceover file"
        )

        music_task = Task(
            description="""
            Search pixabay.com for gentle, ambient royalty-free music that fits the tone
            of the quote and video. Provide download link or audio path.
            """,
            agent=music_selector,
            expected_output="Path to background music file"
        )

        video_task = Task(
            description="""
            Combine the generated image, voiceover, and music into a vertical 9:16
            cinematic video with fade-in/fade-out effects, keeping the video under 15 seconds.
            Use OpenCV, MoviePy, and apply soft transitions and subtitles.
            """,
            agent=video_producer,
            expected_output="Path to final video file"
        )

        posting_task = Task(
            description="""
            Write an Instagram caption and post the video using high-engagement hashtags.
            Keep caption emotionally reflective and relevant.
            """,
            agent=social_media_manager,
            expected_output="Confirmation of Instagram post"
        )

        self.crew = Crew(
            agents=[
                content_researcher,
                quote_creator,
                prompt_engineer,
                voiceover_producer,
                music_selector,
                video_producer,
                social_media_manager
            ],
            tasks=[
                research_task,
                quote_task,
                prompt_task,
                voiceover_task,
                music_task,
                video_task,
                posting_task
            ],
            process=Process.sequential,
            verbose=True
        )

        return self.crew

    def run_pipeline(self):
        if not self.crew:
            self.create_crew()

        result = self.crew.kickoff()
        return result
