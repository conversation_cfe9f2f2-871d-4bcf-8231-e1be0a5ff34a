#!/usr/bin/env python3
"""
Specialized Multimedia CrewAI Agents for <PERSON> Quotes Instagram Bot
Professional agents for advanced content creation with multimedia capabilities
"""

from crewai import Agent, Task, Crew, Process
from tools.multimedia_tools import MidjourneyImageTool, ElevenLabsVoiceTool, PixabayMusicTool
from tools.cinematic_video_tools import CinematicVideoTool
import os


class MultimediaContentCrew:
    """Advanced multimedia content creation crew with specialized agents"""
    
    def __init__(self):
        # Initialize multimedia tools
        self.midjourney_tool = MidjourneyImageTool()
        self.elevenlabs_tool = ElevenLabsVoiceTool()
        self.pixabay_tool = PixabayMusicTool()
        self.cinematic_tool = CinematicVideoTool()
        
        # Create specialized agents
        self.ai_image_specialist = self._create_ai_image_specialist()
        self.voice_synthesis_specialist = self._create_voice_synthesis_specialist()
        self.music_curation_specialist = self._create_music_curation_specialist()
        self.cinematic_video_specialist = self._create_cinematic_video_specialist()
    
    def _create_ai_image_specialist(self) -> Agent:
        """Create AI Image Generation Specialist Agent"""
        return Agent(
            role='AI Image Generation Specialist',
            goal='''Generate high-quality, spiritually-themed background images optimized for Instagram Stories (1080x1920) 
            using advanced AI art direction techniques. Create visually stunning backgrounds that perfectly complement 
            Buddha quotes and spiritual content.''',
            backstory='''You are a professional AI Art Director specializing in Midjourney prompt engineering and 
            spiritual visual aesthetics. You have extensive experience in creating Instagram-optimized content and 
            understand the nuances of visual storytelling for social media. Your expertise includes:
            
            - Advanced Midjourney prompt crafting with technical parameters
            - Color psychology and mood-based visual design
            - Instagram Story format optimization (9:16 aspect ratio)
            - Spiritual and zen aesthetic principles
            - Professional photography composition techniques
            - Brand consistency for spiritual content creators
            
            You always consider the emotional impact of visuals and ensure they enhance rather than distract from 
            the spiritual message. Your images evoke peace, wisdom, and inspiration while maintaining professional 
            quality suitable for social media distribution.''',
            tools=[self.midjourney_tool],
            verbose=True,
            max_iter=3,
            memory=True,
            allow_delegation=False
        )
    
    def _create_voice_synthesis_specialist(self) -> Agent:
        """Create Voice Synthesis Specialist Agent"""
        return Agent(
            role='Voice Synthesis Specialist',
            goal='''Generate high-quality voiceovers in "Grandpa Spuds Oxley" voice style for quote narration, 
            optimizing voice parameters for emotional resonance and clarity. Create compelling audio that brings 
            spiritual quotes to life with perfect pacing and emotional depth.''',
            backstory='''You are a professional Voice Director and Audio Engineer with expertise in ElevenLabs 
            voice synthesis technology. You specialize in spiritual and motivational content narration with 
            deep understanding of:
            
            - Voice parameter optimization (stability, clarity, emotional tone)
            - Dramatic pacing and pause insertion for maximum impact
            - Audio quality optimization for social media platforms
            - Emotional tone matching with quote sentiment and themes
            - Speech enhancement techniques for better comprehension
            - Professional audio production standards
            
            You understand that the human voice is a powerful tool for conveying wisdom and inspiration. Your 
            expertise ensures that every word resonates with the listener, creating an intimate connection between 
            the ancient wisdom and modern audiences. You carefully craft each voiceover to match the emotional 
            journey of the quote, using timing, emphasis, and tonal variations to maximize impact.''',
            tools=[self.elevenlabs_tool],
            verbose=True,
            max_iter=3,
            memory=True,
            allow_delegation=False
        )
    
    def _create_music_curation_specialist(self) -> Agent:
        """Create Music Curation Specialist Agent"""
        return Agent(
            role='Music Curation Specialist',
            goal='''Source and curate royalty-free background music from Pixabay that perfectly complements 
            spiritual and motivational content. Select music that enhances the emotional impact of Buddha quotes 
            while maintaining professional audio quality and proper licensing.''',
            backstory='''You are a professional Audio Director and Music Supervisor with specialized expertise in 
            spiritual and ambient music curation. Your background includes:
            
            - Extensive knowledge of meditation, ambient, and spiritual music genres
            - Audio analysis and quality assessment for social media platforms
            - Music licensing and royalty-free content verification
            - Emotional resonance matching between music and spoken content
            - Audio duration and tempo optimization for video content
            - Professional music production and mixing principles
            
            You understand that music is the emotional foundation of multimedia content. Your selections create 
            the perfect atmospheric backdrop that supports and amplifies the spiritual message without overwhelming 
            the narration. You have an intuitive sense for matching musical moods with quote themes, whether it's 
            peaceful meditation music for contemplative quotes or uplifting instrumentals for inspirational messages.''',
            tools=[self.pixabay_tool],
            verbose=True,
            max_iter=3,
            memory=True,
            allow_delegation=False
        )
    
    def _create_cinematic_video_specialist(self) -> Agent:
        """Create Cinematic Video Production Specialist Agent"""
        return Agent(
            role='Cinematic Video Production Specialist',
            goal='''Create professional cinematic quote videos by combining voiceover, background images, and music 
            with advanced video effects. Produce Instagram-ready content with cinematic quality, including Ken Burns 
            effects, professional transitions, audio ducking, and color grading.''',
            backstory='''You are a professional Video Editor and Motion Graphics Artist with expertise in cinematic 
            video production and social media content optimization. Your comprehensive skill set includes:
            
            - Advanced video editing with MoviePy and OpenCV
            - Cinematic effects implementation (Ken Burns, transitions, color grading)
            - Professional audio mixing and ducking techniques
            - Motion graphics and text animation
            - Instagram technical requirements and optimization
            - Visual storytelling for spiritual and motivational content
            - Professional video production workflows
            
            You approach each video as a complete cinematic experience, carefully orchestrating visual and audio 
            elements to create maximum emotional impact. Your expertise in timing, pacing, and visual composition 
            ensures that every second of the video serves the spiritual message. You understand that great video 
            content is not just about technical excellence, but about creating an emotional journey that resonates 
            with viewers and inspires them to engage with the content.''',
            tools=[self.cinematic_tool],
            verbose=True,
            max_iter=3,
            memory=True,
            allow_delegation=False
        )
    
    def create_multimedia_crew(self) -> Crew:
        """Create the complete multimedia production crew"""
        
        # Define coordinated tasks for multimedia production
        image_generation_task = Task(
            description='''Analyze the provided Buddha quote and generate a high-quality background image that 
            perfectly complements the spiritual message. Consider the quote's theme, emotional tone, and intended 
            impact when crafting the Midjourney prompt. 
            
            Requirements:
            - Create detailed Midjourney prompt with technical parameters (--ar 9:16, --quality 2, --stylize 750)
            - Include appropriate style parameters (cinematic, peaceful, zen, minimalist)
            - Specify lighting conditions (golden hour, soft lighting, ethereal glow)
            - Define color palettes that match the quote's emotional tone
            - Ensure Instagram Story optimization (1080x1920)
            - Avoid any text or letters in the image
            
            Output: High-quality background image file path and detailed prompt used.''',
            agent=self.ai_image_specialist,
            expected_output="Background image file path with detailed Midjourney prompt and technical specifications"
        )
        
        voice_synthesis_task = Task(
            description='''Create a professional voiceover for the Buddha quote using the "Grandpa Spuds Oxley" 
            voice style. Optimize all voice parameters for maximum emotional impact and clarity.
            
            Requirements:
            - Use ElevenLabs API with Grandpa Spuds Oxley voice
            - Optimize voice stability and clarity settings based on quote emotion
            - Add appropriate pacing and dramatic pauses
            - Enhance text with emphasis markers for spiritual keywords
            - Ensure audio quality suitable for social media
            - Match emotional tone with quote sentiment (calm, inspiring, wise, peaceful)
            
            Output: High-quality audio file with optimized voice parameters and timing.''',
            agent=self.voice_synthesis_specialist,
            expected_output="Voiceover audio file path with duration and emotional tone specifications"
        )
        
        music_curation_task = Task(
            description='''Source and select the perfect royalty-free background music that complements the 
            Buddha quote and voiceover. Focus on spiritual, ambient, and meditation genres.
            
            Requirements:
            - Search Pixabay for appropriate spiritual/ambient music
            - Match music genre and mood with quote theme
            - Ensure proper duration compatibility with video length
            - Verify royalty-free licensing and quality
            - Consider emotional resonance with spiritual content
            - Select music that supports rather than competes with voiceover
            
            Output: Downloaded music file with metadata including duration, genre, and mood classification.''',
            agent=self.music_curation_specialist,
            expected_output="Background music file path with genre, mood, and duration specifications"
        )
        
        cinematic_production_task = Task(
            description='''Combine all multimedia elements (background image, voiceover, and music) into a 
            professional cinematic video with advanced effects and transitions.
            
            Requirements:
            - Implement Ken Burns effect on background image (slow zoom/pan)
            - Create animated text overlay with professional typography
            - Apply cinematic effects (vignette, color grading, transitions)
            - Implement professional audio mixing with ducking
            - Ensure Instagram Story format (1080x1920, 15-60 seconds)
            - Add fade in/out transitions for smooth viewing experience
            - Optimize video quality and file size for social media
            
            Output: Final cinematic video file ready for Instagram distribution.''',
            agent=self.cinematic_video_specialist,
            expected_output="Final cinematic video file path with technical specifications and quality metrics",
            context=[image_generation_task, voice_synthesis_task, music_curation_task]
        )
        
        # Create and return the multimedia crew
        return Crew(
            agents=[
                self.ai_image_specialist,
                self.voice_synthesis_specialist, 
                self.music_curation_specialist,
                self.cinematic_video_specialist
            ],
            tasks=[
                image_generation_task,
                voice_synthesis_task,
                music_curation_task,
                cinematic_production_task
            ],
            process=Process.sequential,
            verbose=True,
            memory=True,
            planning=True
        )
    
    def create_cinematic_video(self, quote_text: str, quote_theme: str = "peace", 
                             video_length: int = 30, style: str = "cinematic") -> dict:
        """
        Create a complete cinematic video with all multimedia elements
        
        Args:
            quote_text: The Buddha quote to create video for
            quote_theme: Theme of the quote (peace, wisdom, love, etc.)
            video_length: Desired video length in seconds (15-60)
            style: Video style (cinematic, minimal, dynamic)
        
        Returns:
            Dictionary with all created assets and final video path
        """
        try:
            # Create the multimedia crew
            crew = self.create_multimedia_crew()
            
            # Prepare input data
            inputs = {
                "quote_text": quote_text,
                "quote_theme": quote_theme,
                "video_length": video_length,
                "style": style,
                "mood": self._determine_mood_from_theme(quote_theme)
            }
            
            # Execute the multimedia production pipeline
            result = crew.kickoff(inputs=inputs)
            
            return {
                "success": True,
                "result": result,
                "quote_text": quote_text,
                "theme": quote_theme,
                "style": style,
                "duration": video_length
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "quote_text": quote_text
            }
    
    def _determine_mood_from_theme(self, theme: str) -> str:
        """Determine visual/audio mood from quote theme"""
        mood_mapping = {
            "peace": "peaceful",
            "wisdom": "wise", 
            "love": "inspiring",
            "meditation": "meditative",
            "mindfulness": "zen",
            "gratitude": "inspiring",
            "compassion": "peaceful",
            "enlightenment": "wise"
        }
        
        return mood_mapping.get(theme.lower(), "peaceful")
