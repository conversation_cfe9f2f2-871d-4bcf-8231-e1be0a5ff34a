import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Instagram credentials
    INSTA_USERNAME = os.getenv("INSTA_USERNAME")
    INSTA_PASSWORD = os.getenv("INSTA_PASSWORD")
    
    # API Keys
    ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Paths
    ASSETS_PATH = "assets/"
    TEMP_PATH = "temp/"
    LOGS_PATH = "logs/"
    
    # Posting schedule (IST)
    POSTING_TIMES = ["08:00", "14:00", "20:00"]