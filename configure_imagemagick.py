#!/usr/bin/env python3
"""
ImageMagick Configuration Script for MoviePy on Windows
"""
import os
import sys
import subprocess
from pathlib import Path

def find_imagemagick_paths():
    """Find ImageMagick installation paths on Windows"""
    possible_paths = [
        r"C:\Program Files\ImageMagick-7.1.2-Q16-HDRI",
        r"C:\Program Files\ImageMagick-7.1.2-Q16",
        r"C:\Program Files\ImageMagick-7.1.1-Q16-HDRI",
        r"C:\Program Files\ImageMagick-7.1.1-Q16",
        r"C:\Program Files\ImageMagick-7.1.0-Q16-HDRI",
        r"C:\Program Files\ImageMagick-7.1.0-Q16",
        r"C:\Program Files (x86)\ImageMagick-7.1.2-Q16-HDRI",
        r"C:\Program Files (x86)\ImageMagick-7.1.2-Q16",
        r"C:\Program Files (x86)\ImageMagick-7.1.1-Q16-HDRI",
        r"C:\Program Files (x86)\ImageMagick-7.1.1-Q16",
    ]
    
    found_paths = []
    
    for path in possible_paths:
        if os.path.exists(path):
            magick_exe = os.path.join(path, "magick.exe")
            convert_exe = os.path.join(path, "convert.exe")
            
            if os.path.exists(magick_exe) or os.path.exists(convert_exe):
                found_paths.append({
                    'base_path': path,
                    'magick_exe': magick_exe if os.path.exists(magick_exe) else None,
                    'convert_exe': convert_exe if os.path.exists(convert_exe) else None
                })
    
    return found_paths

def test_imagemagick(executable_path):
    """Test if ImageMagick is working"""
    try:
        result = subprocess.run([executable_path, "-version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, result.stderr
    except Exception as e:
        return False, str(e)

def create_moviepy_config():
    """Create MoviePy configuration file"""
    print("🔧 IMAGEMAGICK CONFIGURATION FOR MOVIEPY")
    print("=" * 50)
    
    # Find ImageMagick installations
    print("🔍 Searching for ImageMagick installations...")
    found_paths = find_imagemagick_paths()
    
    if not found_paths:
        print("❌ No ImageMagick installation found!")
        print("\n📥 Please download and install ImageMagick from:")
        print("   https://imagemagick.org/archive/binaries/ImageMagick-7.1.2-0-Q16-HDRI-x64-dll.exe")
        print("\n⚠️  IMPORTANT: During installation, check 'Install development headers and libraries for C and C++'")
        return False
    
    print(f"✅ Found {len(found_paths)} ImageMagick installation(s)")
    
    # Test installations and find working one
    working_path = None
    for i, path_info in enumerate(found_paths):
        print(f"\n📂 Testing installation {i+1}: {path_info['base_path']}")
        
        # Test magick.exe first (newer versions)
        if path_info['magick_exe']:
            print("   Testing magick.exe...")
            success, output = test_imagemagick(path_info['magick_exe'])
            if success:
                print("   ✅ magick.exe is working!")
                working_path = path_info
                break
            else:
                print(f"   ❌ magick.exe failed: {output[:100]}")
        
        # Test convert.exe as fallback
        if path_info['convert_exe']:
            print("   Testing convert.exe...")
            success, output = test_imagemagick(path_info['convert_exe'])
            if success:
                print("   ✅ convert.exe is working!")
                working_path = path_info
                break
            else:
                print(f"   ❌ convert.exe failed: {output[:100]}")
    
    if not working_path:
        print("❌ No working ImageMagick installation found!")
        return False
    
    # Create MoviePy config
    print(f"\n⚙️  Configuring MoviePy to use: {working_path['base_path']}")
    
    # Get the correct executable path
    if working_path['magick_exe'] and os.path.exists(working_path['magick_exe']):
        imagemagick_binary = working_path['magick_exe']
    elif working_path['convert_exe'] and os.path.exists(working_path['convert_exe']):
        imagemagick_binary = working_path['convert_exe']
    else:
        print("❌ No valid executable found!")
        return False
    
    # Create config content
    config_content = f'''# MoviePy ImageMagick Configuration
# Auto-generated by configure_imagemagick.py

import os

# ImageMagick binary path
IMAGEMAGICK_BINARY = r"{imagemagick_binary}"

# Verify the binary exists
if not os.path.exists(IMAGEMAGICK_BINARY):
    raise FileNotFoundError(f"ImageMagick binary not found at: {{IMAGEMAGICK_BINARY}}")

print(f"MoviePy configured to use ImageMagick at: {{IMAGEMAGICK_BINARY}}")
'''
    
    # Write config file
    try:
        # Try to find moviepy installation directory
        import moviepy
        moviepy_dir = os.path.dirname(moviepy.__file__)
        config_path = os.path.join(moviepy_dir, "config_defaults.py")
        
        # Backup existing config if it exists
        if os.path.exists(config_path):
            backup_path = config_path + ".backup"
            if not os.path.exists(backup_path):
                import shutil
                shutil.copy2(config_path, backup_path)
                print(f"📋 Backed up existing config to: {backup_path}")
        
        # Write new config
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        print(f"✅ MoviePy config written to: {config_path}")
        
    except Exception as e:
        print(f"⚠️  Could not write to MoviePy directory: {e}")
        print("📝 Creating local config file instead...")
        
        # Create local config file
        local_config_path = "moviepy_config.py"
        with open(local_config_path, 'w') as f:
            f.write(config_content)
        
        print(f"✅ Local config written to: {local_config_path}")
        print("💡 Import this in your video tools: from moviepy_config import IMAGEMAGICK_BINARY")
    
    # Test MoviePy with ImageMagick
    print("\n🧪 Testing MoviePy with ImageMagick...")
    try:
        from moviepy.config import check
        check()
        print("✅ MoviePy ImageMagick integration test passed!")
    except Exception as e:
        print(f"⚠️  MoviePy test failed: {e}")
        print("💡 You may need to restart Python for changes to take effect")
    
    return True

def main():
    """Main configuration function"""
    try:
        success = create_moviepy_config()
        
        if success:
            print("\n🎉 ImageMagick configuration completed successfully!")
            print("\n📋 Next steps:")
            print("   1. Restart your Python application")
            print("   2. Test video creation functionality")
            print("   3. Check that text overlays work properly")
        else:
            print("\n❌ Configuration failed!")
            print("\n🔧 Manual setup required:")
            print("   1. Install ImageMagick from the official website")
            print("   2. Add ImageMagick to your system PATH")
            print("   3. Restart your computer")
            print("   4. Run this script again")
        
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
