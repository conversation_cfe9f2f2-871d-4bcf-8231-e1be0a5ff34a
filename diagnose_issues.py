#!/usr/bin/env python3
"""
Diagnostic script to identify issues with <PERSON> Quotes Instagram Bot
"""
import os
import sys
import traceback
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imports():
    """Test all required imports"""
    print("🔍 TESTING IMPORTS")
    print("=" * 40)
    
    imports_to_test = [
        ("crewai", "from crewai import Agent, Task, Crew, Process"),
        ("content_tools", "from tools.content_tools import TrendR<PERSON>archTool, QuoteGeneratorTool, SocialMediaTrendsTool"),
        ("video_tools", "from tools.video_tools import VideoCreatorTool"),
        ("instagram_tools", "from tools.instagram_tools import InstagramPostTool"),
        ("multimedia_tools", "from tools.multimedia_tools import MidjourneyImageTool, ElevenLabsVoiceTool, PixabayMusicTool"),
        ("crew_agents", "from agents.crew_agents import BuddhaQuote<PERSON><PERSON>"),
    ]
    
    results = {}
    
    for name, import_statement in imports_to_test:
        try:
            exec(import_statement)
            print(f"   ✅ {name}: OK")
            results[name] = True
        except Exception as e:
            print(f"   ❌ {name}: {str(e)}")
            results[name] = False
    
    return results

def test_environment_variables():
    """Test environment variable configuration"""
    print("\n🔑 TESTING ENVIRONMENT VARIABLES")
    print("=" * 45)
    
    required_vars = [
        "OPENAI_API_KEY",
        "ELEVENLABS_API_KEY", 
        "RAPIDAPI_KEY"
    ]
    
    optional_vars = [
        "MIDJOURNEY_API_KEY",
        "PIXABAY_API_KEY",
        "INSTAGRAM_USERNAME",
        "INSTAGRAM_PASSWORD"
    ]
    
    results = {}
    
    print("   Required Variables:")
    for var in required_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}':
            print(f"   ✅ {var}: Configured")
            results[var] = True
        else:
            print(f"   ❌ {var}: Not configured")
            results[var] = False
    
    print("\n   Optional Variables:")
    for var in optional_vars:
        value = os.getenv(var)
        if value and value != f'your_{var.lower()}':
            print(f"   ✅ {var}: Configured")
            results[var] = True
        else:
            print(f"   ⚠️  {var}: Not configured")
            results[var] = False
    
    return results

def test_tool_initialization():
    """Test individual tool initialization"""
    print("\n🛠️  TESTING TOOL INITIALIZATION")
    print("=" * 45)
    
    tools_to_test = [
        ("TrendResearchTool", "from tools.content_tools import TrendResearchTool; tool = TrendResearchTool()"),
        ("QuoteGeneratorTool", "from tools.content_tools import QuoteGeneratorTool; tool = QuoteGeneratorTool()"),
        ("SocialMediaTrendsTool", "from tools.content_tools import SocialMediaTrendsTool; tool = SocialMediaTrendsTool()"),
        ("VideoCreatorTool", "from tools.video_tools import VideoCreatorTool; tool = VideoCreatorTool()"),
        ("InstagramPostTool", "from tools.instagram_tools import InstagramPostTool; tool = InstagramPostTool()"),
    ]
    
    results = {}
    
    for name, init_code in tools_to_test:
        try:
            exec(init_code)
            print(f"   ✅ {name}: Initialized successfully")
            results[name] = True
        except Exception as e:
            print(f"   ❌ {name}: {str(e)}")
            results[name] = False
    
    return results

def test_crew_initialization():
    """Test CrewAI crew initialization"""
    print("\n🤖 TESTING CREWAI INITIALIZATION")
    print("=" * 45)
    
    try:
        print("   📦 Importing BuddhaQuoteCrew...")
        from agents.crew_agents import BuddhaQuoteCrew
        print("   ✅ Import successful")
        
        print("   🏗️  Creating BuddhaQuoteCrew instance...")
        buddha_crew = BuddhaQuoteCrew()
        print("   ✅ Instance created")
        
        print("   ⚙️  Creating crew...")
        crew = buddha_crew.create_crew()
        print("   ✅ Crew created successfully")
        print(f"   📊 Agents: {len(crew.agents)}")
        print(f"   📊 Tasks: {len(crew.tasks)}")
        
        # Test agent details
        print("\n   👥 Agent Details:")
        for i, agent in enumerate(crew.agents):
            print(f"      {i+1}. {agent.role}")
            print(f"         Tools: {len(agent.tools) if hasattr(agent, 'tools') and agent.tools else 0}")
        
        return True, crew
        
    except Exception as e:
        print(f"   ❌ CrewAI initialization failed: {str(e)}")
        traceback.print_exc()
        return False, None

def test_simple_crew_execution():
    """Test a simple crew execution without full pipeline"""
    print("\n🚀 TESTING SIMPLE CREW EXECUTION")
    print("=" * 45)
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        print("   🏗️  Creating minimal test crew...")
        buddha_crew = BuddhaQuoteCrew()
        
        # Try to create just the crew without running it
        crew = buddha_crew.create_crew()
        print("   ✅ Crew created for testing")
        
        # Test individual agent initialization
        print("   🧪 Testing individual agents...")
        for i, agent in enumerate(crew.agents):
            print(f"      Agent {i+1}: {agent.role} - ✅ OK")
        
        print("   ⚠️  Skipping full execution to avoid hanging")
        return True
        
    except Exception as e:
        print(f"   ❌ Simple crew test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_individual_tool_execution():
    """Test individual tool execution"""
    print("\n🔧 TESTING INDIVIDUAL TOOL EXECUTION")
    print("=" * 45)
    
    try:
        # Test QuoteGeneratorTool
        print("   📝 Testing QuoteGeneratorTool...")
        from tools.content_tools import QuoteGeneratorTool
        quote_tool = QuoteGeneratorTool()
        
        # Test with a simple input
        result = quote_tool._run("peace")
        print(f"   ✅ Quote generated: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Tool execution failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all diagnostic tests"""
    print("🔍 BUDDHA QUOTES BOT DIAGNOSTIC SUITE")
    print("=" * 60)
    
    # Test 1: Imports
    import_results = test_imports()
    
    # Test 2: Environment Variables
    env_results = test_environment_variables()
    
    # Test 3: Tool Initialization
    tool_results = test_tool_initialization()
    
    # Test 4: CrewAI Initialization
    crew_success, crew = test_crew_initialization()
    
    # Test 5: Simple Crew Execution
    simple_exec_success = test_simple_crew_execution()
    
    # Test 6: Individual Tool Execution
    tool_exec_success = test_individual_tool_execution()
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 40)
    
    print(f"Imports: {'✅ PASS' if all(import_results.values()) else '❌ FAIL'}")
    print(f"Environment: {'✅ PASS' if all(env_results[k] for k in ['OPENAI_API_KEY', 'ELEVENLABS_API_KEY', 'RAPIDAPI_KEY']) else '❌ FAIL'}")
    print(f"Tool Init: {'✅ PASS' if all(tool_results.values()) else '❌ FAIL'}")
    print(f"CrewAI Init: {'✅ PASS' if crew_success else '❌ FAIL'}")
    print(f"Simple Execution: {'✅ PASS' if simple_exec_success else '❌ FAIL'}")
    print(f"Tool Execution: {'✅ PASS' if tool_exec_success else '❌ FAIL'}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 30)
    
    if not all(import_results.values()):
        print("🔧 Fix import issues first")
    
    if not all(env_results[k] for k in ['OPENAI_API_KEY', 'ELEVENLABS_API_KEY', 'RAPIDAPI_KEY']):
        print("🔑 Configure required API keys")
    
    if not crew_success:
        print("🤖 Fix CrewAI initialization issues")
    
    if crew_success and not simple_exec_success:
        print("⚙️  Check agent/task configuration")
    
    print("\n🎯 Next steps: Address issues in order of priority")

if __name__ == "__main__":
    main()
