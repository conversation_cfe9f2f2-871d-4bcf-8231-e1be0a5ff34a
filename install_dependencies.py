#!/usr/bin/env python3
"""
Quick dependency installer for <PERSON> Quotes Instagram Bot
Installs the essential packages needed for the bot to run
"""

import subprocess
import sys
import os

def install_package(package_name):
    """Install a single package using pip"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ], stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
        print(f"✓ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package_name}")
        if e.stderr:
            print(f"  Error: {e.stderr.decode()}")
        return False

def main():
    """Install all required dependencies"""
    print("BUDDHA QUOTES BOT - DEPENDENCY INSTALLER")
    print("=" * 50)
    print("Installing essential packages for the bot to run...")
    print()
    
    # Essential packages for the bot to work
    essential_packages = [
        "langchain-openai",
        "langchain-community",
        "crewai",
        "python-dotenv",
        "requests",
        "pillow",
        "schedule"
    ]
    
    # Optional packages for full functionality
    optional_packages = [
        "moviepy",
        "opencv-python",
        "instagrapi"
    ]
    
    print("Installing essential packages...")
    essential_success = 0
    for package in essential_packages:
        if install_package(package):
            essential_success += 1
    
    print(f"\nEssential packages: {essential_success}/{len(essential_packages)} installed")
    
    if essential_success == len(essential_packages):
        print("✓ All essential packages installed successfully!")
        print("\nInstalling optional packages...")
        
        optional_success = 0
        for package in optional_packages:
            if install_package(package):
                optional_success += 1
        
        print(f"\nOptional packages: {optional_success}/{len(optional_packages)} installed")
        
        print("\n" + "=" * 50)
        print("INSTALLATION COMPLETE!")
        print("\nNext steps:")
        print("1. Configure your API keys in the .env file")
        print("2. Test the bot: python run_local.py")
        print("3. For production: python run_production.py")
        
    else:
        print("\n✗ Some essential packages failed to install")
        print("Please check your internet connection and try again")
        print("Or install manually: pip install langchain-openai langchain-community crewai")

if __name__ == "__main__":
    main()
