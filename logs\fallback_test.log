2025-07-20 14:16:23,822 - FallbackTest - INFO - ============================================================
2025-07-20 14:16:23,826 - FallbackTest - INFO - ==================================================
2025-07-20 14:16:23,901 - FallbackTest - INFO - ==================================================
2025-07-20 14:16:23,971 - FallbackTest - INFO -       Error: 'Rate limit reached for gpt-4o-mini...' -> Rate limit: True
2025-07-20 14:16:23,972 - FallbackTest - INFO -       Error: 'HTTP 429: Too many requests...' -> Rate limit: True
2025-07-20 14:16:23,972 - FallbackTest - INFO -       Error: 'quota exceeded...' -> Rate limit: True
2025-07-20 14:16:23,972 - FallbackTest - INFO -       Error: 'Some other error...' -> Rate limit: False
2025-07-20 14:16:23,992 - FallbackTest - INFO - ==================================================
2025-07-20 14:16:30,066 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-20 14:16:32,759 - FallbackTest - INFO - ==================================================
2025-07-20 14:16:33,929 - FallbackTest - INFO - ==================================================
2025-07-20 14:16:33,937 - FallbackTest - INFO -       Attempt 1: Simulating rate limit...
2025-07-20 14:16:33,939 - FallbackTest - INFO -       Result: Rate limit hit for openai, no fallback available
2025-07-20 14:16:34,941 - FallbackTest - INFO -       Attempt 2: Simulating rate limit...
2025-07-20 14:16:34,944 - FallbackTest - INFO -       Result: Rate limit hit for openai, no fallback available
2025-07-20 14:16:35,947 - FallbackTest - INFO -       Attempt 3: Simulating rate limit...
2025-07-20 14:16:35,954 - FallbackTest - INFO -       Result: Rate limit hit for openai, no fallback available
2025-07-20 14:16:36,966 - FallbackTest - INFO -       openai: {'requests': 0, 'errors': 3, 'rate_limits': 3}
2025-07-20 14:16:36,966 - FallbackTest - INFO -       deepseek: {'requests': 0, 'errors': 0, 'rate_limits': 0}
2025-07-20 14:16:36,969 - FallbackTest - INFO - ========================================
2025-07-20 14:16:36,980 - FallbackTest - INFO -    - Ensure both OpenAI and DeepSeek API keys are configured
2025-07-20 14:16:36,980 - FallbackTest - INFO -    - Verify all required dependencies are installed
