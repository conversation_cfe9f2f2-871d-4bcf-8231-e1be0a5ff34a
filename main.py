import schedule
import time
import os
import sys
from datetime import datetime
from agents.crew_agents import Buddha<PERSON><PERSON><PERSON><PERSON><PERSON>
from tools.content_tools import TrendResearchTool, QuoteGeneratorTool
from tools.video_tools import VideoCreatorTool
from tools.instagram_tools import InstagramPostTool
from instagrapi import Client
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class BuddhaBot:
    def __init__(self):
        self.setup_logging()
        self.environment = self.detect_environment()
        self.components_verified = False

    def setup_logging(self):
        """Setup comprehensive logging system"""
        os.makedirs("logs", exist_ok=True)

        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # Setup root logger
        self.logger = logging.getLogger('BuddhaBot')
        self.logger.setLevel(logging.INFO)

        # File handler for detailed logs
        file_handler = logging.FileHandler('logs/buddha_bot.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)

        # Console handler for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Prevent duplicate logs
        self.logger.propagate = False

    def detect_environment(self):
        """Detect if running in development or production mode"""
        # Check for common production environment indicators
        is_production = any([
            os.getenv('ENVIRONMENT') == 'production',
            os.getenv('PROD') == 'true',
            os.path.exists('/.dockerenv'),  # Running in Docker
            os.getenv('RAILWAY_ENVIRONMENT'),  # Railway deployment
            os.getenv('HEROKU_APP_NAME'),  # Heroku deployment
            '--production' in sys.argv
        ])

        environment = 'production' if is_production else 'development'
        self.logger.info(f"🔍 Environment detected: {environment.upper()}")
        return environment

    def verify_components(self):
        """Verify all system components are properly initialized"""
        self.logger.info("🔧 Verifying system components...")

        verification_results = {
            'environment_vars': False,
            'directories': False,
            'tools': False,
            'instagram_connection': False,
            'crewai_agents': False
        }

        try:
            # 1. Verify environment variables
            self.logger.info("   📋 Checking environment variables...")
            required_vars = ['INSTA_USERNAME', 'INSTA_PASSWORD']
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                self.logger.warning(f"   ⚠️  Missing environment variables: {missing_vars}")
                if self.environment == 'development':
                    self.logger.info("   💡 Running in development mode - some features may be limited")
                verification_results['environment_vars'] = len(missing_vars) == 0
            else:
                self.logger.info("   ✅ Environment variables configured")
                verification_results['environment_vars'] = True

            # 2. Verify directories
            self.logger.info("   📁 Checking required directories...")
            required_dirs = ['logs', 'temp', 'assets', 'assets/backgrounds']
            for directory in required_dirs:
                os.makedirs(directory, exist_ok=True)
            self.logger.info("   ✅ Directory structure verified")
            verification_results['directories'] = True

            # 3. Verify CrewAI tools
            self.logger.info("   🛠️  Initializing CrewAI tools...")
            try:
                trend_tool = TrendResearchTool()
                quote_tool = QuoteGeneratorTool()
                video_tool = VideoCreatorTool()
                instagram_tool = InstagramPostTool()
                self.logger.info("   ✅ CrewAI tools initialized successfully")
                verification_results['tools'] = True
            except Exception as e:
                self.logger.error(f"   ❌ CrewAI tools initialization failed: {str(e)}")
                verification_results['tools'] = False

            # 4. Verify Instagram connection (only if credentials available)
            if verification_results['environment_vars']:
                self.logger.info("   📱 Testing Instagram connection...")
                try:
                    cl = Client()
                    # Test login without actually logging in for verification
                    username = os.getenv('INSTA_USERNAME')
                    if username:
                        self.logger.info(f"   ✅ Instagram credentials configured for: {username}")
                        verification_results['instagram_connection'] = True
                    else:
                        self.logger.warning("   ⚠️  Instagram username not configured")
                except Exception as e:
                    self.logger.warning(f"   ⚠️  Instagram connection test failed: {str(e)}")
                    verification_results['instagram_connection'] = False

            # 5. Verify CrewAI agents
            self.logger.info("   🤖 Initializing CrewAI agents...")
            try:
                buddha_crew = BuddhaQuoteCrew()
                crew = buddha_crew.create_crew()
                if crew and len(crew.agents) == 4:
                    self.logger.info("   ✅ CrewAI agents initialized (4 agents ready)")
                    verification_results['crewai_agents'] = True
                else:
                    self.logger.error("   ❌ CrewAI agents initialization incomplete")
                    verification_results['crewai_agents'] = False
            except Exception as e:
                self.logger.error(f"   ❌ CrewAI agents initialization failed: {str(e)}")
                verification_results['crewai_agents'] = False

        except Exception as e:
            self.logger.error(f"Component verification failed: {str(e)}")

        # Summary
        verified_count = sum(verification_results.values())
        total_count = len(verification_results)

        self.logger.info(f"🔍 Component verification complete: {verified_count}/{total_count} components ready")

        if verified_count == total_count:
            self.logger.info("✅ All components verified successfully!")
            self.components_verified = True
        elif verified_count >= 3:  # Minimum viable components
            self.logger.warning("⚠️  Some components have issues but system can proceed")
            self.components_verified = True
        else:
            self.logger.error("❌ Critical components failed - system may not function properly")
            self.components_verified = False

        return verification_results

    def run_content_pipeline(self, is_test_run=False):
        """Execute the CrewAI content creation pipeline"""
        run_type = "TEST RUN" if is_test_run else "SCHEDULED RUN"
        self.logger.info(f"🚀 Starting {run_type} at {datetime.now()}")

        try:
            # Initialize CrewAI
            self.logger.info("   🤖 Initializing Buddha Quote CrewAI...")
            buddha_crew = BuddhaQuoteCrew()

            # Execute pipeline
            self.logger.info("   ⚙️  Executing CrewAI pipeline...")
            result = buddha_crew.run_pipeline()

            self.logger.info(f"✅ {run_type} completed successfully!")
            self.logger.info(f"   📊 Result: {result}")

            return True, result

        except Exception as e:
            self.logger.error(f"❌ {run_type} failed: {str(e)}")
            self.logger.error(f"   🔍 Error details: {type(e).__name__}")
            return False, str(e)

    def run_immediate_test(self):
        """Run immediate test for development verification"""
        self.logger.info("🧪 IMMEDIATE TESTING MODE ACTIVATED")
        self.logger.info("=" * 50)

        if not self.components_verified:
            self.logger.warning("⚠️  Components not fully verified - test may fail")

        success, result = self.run_content_pipeline(is_test_run=True)

        if success:
            self.logger.info("🎉 Test run successful! System is ready for production.")
        else:
            self.logger.error("💥 Test run failed! Check logs for details.")
            self.logger.info("🔄 Will still proceed to start scheduler for production mode...")

        self.logger.info("=" * 50)
        return success

    def schedule_production_runs(self):
        """Setup production scheduling"""
        self.logger.info("📅 PRODUCTION SCHEDULING MODE ACTIVATED")

        # Schedule posts at IST times
        schedule.every().day.at("08:00").do(self.run_content_pipeline, is_test_run=False)
        schedule.every().day.at("14:00").do(self.run_content_pipeline, is_test_run=False)
        schedule.every().day.at("20:00").do(self.run_content_pipeline, is_test_run=False)

        self.logger.info("⏰ Scheduled posts for:")
        self.logger.info("   • 8:00 AM IST")
        self.logger.info("   • 2:00 PM IST")
        self.logger.info("   • 8:00 PM IST")

    def run_scheduler_loop(self):
        """Run the main scheduler loop"""
        self.logger.info("🔄 Starting scheduler loop...")
        self.logger.info("   💡 Press Ctrl+C to stop the bot")

        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

        except KeyboardInterrupt:
            self.logger.info("🛑 Bot stopped by user")
        except Exception as e:
            self.logger.error(f"💥 Scheduler error: {str(e)}")
            self.logger.info("🔄 Restarting scheduler in 5 minutes...")
            time.sleep(300)  # Wait 5 minutes before restart
            self.run_scheduler_loop()  # Recursive restart

    def run(self):
        """Main execution method"""
        self.logger.info("🌟 BUDDHA QUOTES INSTAGRAM BOT STARTING")
        self.logger.info("=" * 60)

        # Verify components
        verification_results = self.verify_components()

        if self.environment == 'development':
            self.logger.info("🧪 Development mode: Running immediate test...")
            test_success = self.run_immediate_test()

            # Ask user if they want to continue to production mode
            if test_success:
                self.logger.info("✅ Test successful! Proceeding to production scheduling...")
            else:
                self.logger.warning("⚠️  Test failed but continuing to production scheduling...")

        # Setup production scheduling
        self.schedule_production_runs()

        # Start scheduler loop
        self.run_scheduler_loop()

def main():
    """Entry point"""
    try:
        bot = BuddhaBot()
        bot.run()
    except Exception as e:
        print(f"💥 Critical error starting bot: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
