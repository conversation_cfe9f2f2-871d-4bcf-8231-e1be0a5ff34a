import schedule
import time
import os
import sys
from datetime import datetime
from agents.crew_agents import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tools.content_tools import TrendR<PERSON>archTool, QuoteGeneratorTool
from tools.video_tools import VideoCreatorTool
from tools.instagram_tools import InstagramPostTool
from instagrapi import Client
import logging
from dotenv import load_dotenv

# Import LLM fallback management
from utils.llm_fallback_manager import LLMFallbackManager, LLMProvider
from utils.crewai_config_manager import CrewAIConfigManager

# Load environment variables
load_dotenv()

class BuddhaBot:
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger('BuddhaBot')
        self.environment = self.detect_environment()

        # Initialize LLM fallback management
        self.crewai_manager = CrewAIConfigManager(self.logger)

        self.components_verified = False

    def setup_logging(self):
        """Setup comprehensive logging system"""
        os.makedirs("logs", exist_ok=True)

        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # Setup root logger
        self.logger = logging.getLogger('BuddhaBot')
        self.logger.setLevel(logging.INFO)

        # File handler for detailed logs
        file_handler = logging.FileHandler('logs/buddha_bot.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)

        # Console handler for immediate feedback with UTF-8 encoding
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)

        # Set UTF-8 encoding for console output on Windows
        if sys.platform.startswith('win'):
            import codecs
            sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Prevent duplicate logs
        self.logger.propagate = False

    def detect_environment(self):
        """Detect if running in development or production mode"""
        # Check for common production environment indicators
        is_production = any([
            os.getenv('ENVIRONMENT') == 'production',
            os.getenv('PROD') == 'true',
            os.path.exists('/.dockerenv'),  # Running in Docker
            os.getenv('RAILWAY_ENVIRONMENT'),  # Railway deployment
            os.getenv('HEROKU_APP_NAME'),  # Heroku deployment
            '--production' in sys.argv
        ])

        environment = 'production' if is_production else 'development'
        self.logger.info(f"[INFO] Environment detected: {environment.upper()}")
        return environment

    def verify_components(self):
        """Verify all system components are properly initialized"""
        self.logger.info("[SETUP] Verifying system components...")

        verification_results = {
            'environment_vars': False,
            'directories': False,
            'tools': False,
            'instagram_connection': False,
            'crewai_agents': False
        }

        try:
            # 1. Verify environment variables
            self.logger.info("   [CHECK] Checking environment variables...")
            required_vars = ['INSTA_USERNAME', 'INSTA_PASSWORD']
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                self.logger.warning(f"   [WARN] Missing environment variables: {missing_vars}")
                if self.environment == 'development':
                    self.logger.info("   [INFO] Running in development mode - some features may be limited")
                verification_results['environment_vars'] = len(missing_vars) == 0
            else:
                self.logger.info("   [OK] Environment variables configured")
                verification_results['environment_vars'] = True

            # 2. Verify directories
            self.logger.info("   [CHECK] Checking required directories...")
            required_dirs = ['logs', 'temp', 'assets', 'assets/backgrounds']
            for directory in required_dirs:
                os.makedirs(directory, exist_ok=True)
            self.logger.info("   [OK] Directory structure verified")
            verification_results['directories'] = True

            # 3. Verify CrewAI tools
            self.logger.info("   [CHECK] Initializing CrewAI tools...")
            try:
                trend_tool = TrendResearchTool()
                quote_tool = QuoteGeneratorTool()
                video_tool = VideoCreatorTool()
                instagram_tool = InstagramPostTool()
                self.logger.info("   [OK] CrewAI tools initialized successfully")
                verification_results['tools'] = True
            except Exception as e:
                self.logger.error(f"   [ERROR] CrewAI tools initialization failed: {str(e)}")
                verification_results['tools'] = False

            # 4. Verify Instagram connection (only if credentials available)
            if verification_results['environment_vars']:
                self.logger.info("   [CHECK] Testing Instagram connection...")
                try:
                    # Just check if credentials exist, don't actually connect
                    username = os.getenv('INSTA_USERNAME')
                    password = os.getenv('INSTA_PASSWORD')
                    if username and password and username != 'your_instagram_username':
                        self.logger.info(f"   [OK] Instagram credentials configured for: {username}")
                        verification_results['instagram_connection'] = True
                    else:
                        self.logger.warning("   [WARN] Instagram username not configured")
                        verification_results['instagram_connection'] = False
                except Exception as e:
                    self.logger.warning(f"   [WARN] Instagram connection test failed: {str(e)}")
                    verification_results['instagram_connection'] = False

            # 5. Verify CrewAI agents
            self.logger.info("   [CHECK] Initializing CrewAI agents...")
            try:
                buddha_crew = BuddhaQuoteCrew()
                crew = buddha_crew.create_crew()
                if crew and len(crew.agents) >= 4:
                    self.logger.info(f"   [OK] CrewAI agents initialized ({len(crew.agents)} agents ready)")
                    verification_results['crewai_agents'] = True
                else:
                    self.logger.error(f"   [ERROR] CrewAI agents initialization incomplete (found {len(crew.agents) if crew else 0} agents)")
                    verification_results['crewai_agents'] = False
            except Exception as e:
                self.logger.error(f"   [ERROR] CrewAI agents initialization failed: {str(e)}")
                verification_results['crewai_agents'] = False

        except Exception as e:
            self.logger.error(f"Component verification failed: {str(e)}")

        # Summary
        verified_count = sum(verification_results.values())
        total_count = len(verification_results)

        self.logger.info(f"🔍 Component verification complete: {verified_count}/{total_count} components ready")

        if verified_count == total_count:
            self.logger.info("✅ All components verified successfully!")
            self.components_verified = True
        elif verified_count >= 3:  # Minimum viable components
            self.logger.warning("⚠️  Some components have issues but system can proceed")
            self.components_verified = True
        else:
            self.logger.error("❌ Critical components failed - system may not function properly")
            self.components_verified = False

        return verification_results

    def run_content_pipeline(self, is_test_run=False):
        """Execute the CrewAI content creation pipeline with LLM fallback"""
        run_type = "TEST RUN" if is_test_run else "SCHEDULED RUN"
        self.logger.info(f"🚀 Starting {run_type} at {datetime.now()}")

        try:
            # Log current LLM provider status
            provider_info = self.crewai_manager.get_current_provider_info()
            current_provider = provider_info['current_provider']
            self.logger.info(f"   🤖 Using {current_provider} LLM for CrewAI execution")

            # Initialize CrewAI with current LLM configuration
            self.logger.info("   🏗️  Initializing Buddha Quote CrewAI...")
            buddha_crew = BuddhaQuoteCrew()

            # Create crew with fallback-enabled configuration
            crew = buddha_crew.create_crew()
            if not crew:
                raise ValueError("Failed to create CrewAI crew")

            # Execute pipeline with automatic fallback handling
            self.logger.info("   ⚙️  Executing CrewAI pipeline with fallback support...")
            self.logger.info("   ⏳ This may take several minutes due to API processing...")

            # Use the CrewAI manager's execute_with_fallback method
            success, result = self.crewai_manager.execute_with_fallback(crew, max_retries=2)

            if success:
                self.logger.info(f"✅ {run_type} completed successfully!")
                self.logger.info(f"   📊 Result: {str(result)[:200]}...")

                # Log final provider usage stats
                final_stats = self.crewai_manager.get_current_provider_info()
                final_provider = final_stats['current_provider']
                if final_provider != current_provider:
                    self.logger.info(f"   🔄 Execution completed using {final_provider} (switched from {current_provider})")

                return True, result
            else:
                raise Exception(f"Pipeline execution failed: {result}")

        except Exception as e:
            self.logger.error(f"❌ {run_type} failed: {str(e)}")
            self.logger.error(f"   🔍 Error details: {type(e).__name__}")

            # Provide helpful error context
            error_msg = str(e).lower()
            if "rate limit" in error_msg:
                self.logger.info("   💡 Rate limit encountered - fallback mechanism should have activated")
                self.logger.info("   💡 Consider upgrading API plans or reducing request frequency")
            elif "api" in error_msg and "key" in error_msg:
                self.logger.info("   💡 API key issue detected - check your .env configuration")
            elif "timeout" in error_msg:
                self.logger.info("   💡 Request timeout - API may be experiencing high load")

            # Log current provider stats for debugging
            try:
                stats = self.crewai_manager.get_current_provider_info()
                self.logger.info(f"   📊 Provider stats: {stats['usage_stats']}")
            except:
                pass

            return False, str(e)

    def run_immediate_test(self):
        """Run immediate test for development verification"""
        self.logger.info("🧪 IMMEDIATE TESTING MODE ACTIVATED")
        self.logger.info("=" * 50)

        if not self.components_verified:
            self.logger.warning("⚠️  Components not fully verified - test may fail")

        success, result = self.run_content_pipeline(is_test_run=True)

        if success:
            self.logger.info("🎉 Test run successful! System is ready for production.")
        else:
            self.logger.error("💥 Test run failed! Check logs for details.")
            self.logger.info("🔄 Will still proceed to start scheduler for production mode...")

        self.logger.info("=" * 50)
        return success

    def schedule_production_runs(self):
        """Setup production scheduling"""
        self.logger.info("📅 PRODUCTION SCHEDULING MODE ACTIVATED")

        # Schedule posts at IST times
        schedule.every().day.at("08:00").do(self.run_content_pipeline, is_test_run=False)
        schedule.every().day.at("14:00").do(self.run_content_pipeline, is_test_run=False)
        schedule.every().day.at("20:00").do(self.run_content_pipeline, is_test_run=False)

        self.logger.info("⏰ Scheduled posts for:")
        self.logger.info("   • 8:00 AM IST")
        self.logger.info("   • 2:00 PM IST")
        self.logger.info("   • 8:00 PM IST")

    def run_scheduler_loop(self):
        """Run the main scheduler loop"""
        self.logger.info("🔄 Starting scheduler loop...")
        self.logger.info("   💡 Press Ctrl+C to stop the bot")

        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

        except KeyboardInterrupt:
            self.logger.info("🛑 Bot stopped by user")
        except Exception as e:
            self.logger.error(f"💥 Scheduler error: {str(e)}")
            self.logger.info("🔄 Restarting scheduler in 5 minutes...")
            time.sleep(300)  # Wait 5 minutes before restart
            self.run_scheduler_loop()  # Recursive restart

    def run(self):
        """Main execution method"""
        self.logger.info("🌟 BUDDHA QUOTES INSTAGRAM BOT STARTING")
        self.logger.info("=" * 60)

        # Verify components
        verification_results = self.verify_components()

        if self.environment == 'development':
            self.logger.info("🧪 Development mode: Running immediate test...")
            test_success = self.run_immediate_test()

            # Ask user if they want to continue to production mode
            if test_success:
                self.logger.info("✅ Test successful! Proceeding to production scheduling...")
            else:
                self.logger.warning("⚠️  Test failed but continuing to production scheduling...")

        # Setup production scheduling
        self.schedule_production_runs()

        # Start scheduler loop
        self.run_scheduler_loop()

def main():
    """Entry point"""
    try:
        bot = BuddhaBot()
        bot.run()
    except Exception as e:
        print(f"💥 Critical error starting bot: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
