#!/usr/bin/env python3
"""
Local Development Runner for <PERSON> Quotes Instagram Bot
Forces the bot to run in local mode (execute once and exit)
"""

import os
import sys
from dotenv import load_dotenv

def main():
    """Run the bot in local development mode"""
    print("🏠 BUDDHA QUOTES BOT - LOCAL MODE")
    print("=" * 50)
    print("   📍 Running in local development mode")
    print("   🔄 Will execute content pipeline once and exit")
    print("   💡 No scheduling - immediate execution only")
    print()
    
    # Force development environment
    os.environ['ENVIRONMENT'] = 'development'
    
    # Load environment variables
    load_dotenv()
    
    # Import and run the bot
    try:
        from main import BuddhaBot
        
        bot = BuddhaBot()
        success = bot.run()
        
        if success:
            print("\n🎉 Local execution completed successfully!")
            print("   📁 Check the temp/ and assets/ directories for generated content")
            sys.exit(0)
        else:
            print("\n💥 Local execution failed!")
            print("   🔍 Check the logs for error details")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Execution interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Critical error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
