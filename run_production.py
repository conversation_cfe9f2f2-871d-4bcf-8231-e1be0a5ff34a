#!/usr/bin/env python3
"""
Production Runner for <PERSON> Quotes Instagram Bot
Forces the bot to run in production mode with scheduling
"""

import os
import sys
from dotenv import load_dotenv

def main():
    """Run the bot in production mode with scheduling"""
    print("🏭 BUDDHA QUOTES BOT - PRODUCTION MODE")
    print("=" * 60)
    print("   📍 Running in production mode with scheduling")
    print("   ⏰ Will run continuously with scheduled posts")
    print("   📅 Scheduled times: 8:00 AM, 2:00 PM, 8:00 PM IST")
    print("   🔄 Press Ctrl+C to stop the bot")
    print()
    
    # Force production environment
    os.environ['ENVIRONMENT'] = 'production'
    
    # Load environment variables
    load_dotenv()
    
    # Import and run the bot
    try:
        from main import BuddhaBot
        
        bot = BuddhaBot()
        bot.run()  # This will run continuously with scheduling
        
    except KeyboardInterrupt:
        print("\n🛑 Production bot stopped by user")
        print("   📊 Check logs for execution history")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Critical error in production mode: {str(e)}")
        print("   🔄 Consider restarting the bot")
        sys.exit(1)

if __name__ == "__main__":
    main()
