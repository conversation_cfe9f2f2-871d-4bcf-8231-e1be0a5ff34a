#!/usr/bin/env python3
import os
import subprocess
import sys

def setup_project():
    """Setup the Buddha Quote Instagram Bot project"""

    print("🚀 Setting up Buddha Quote Instagram Bot with CrewAI...")
    print("=" * 60)

    # Create directories
    directories = [
        "agents", "config", "tools", "assets/backgrounds",
        "assets/chinese_flute_sounds", "assets/fonts",
        "logs", "temp"
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")

    # Update pip
    print("\n📦 Updating pip...")
    subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])

    # Install dependencies
    print("📦 Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Python dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return

    # Install Playwright browsers
    print("🌐 Installing Playwright browsers...")
    try:
        subprocess.run(["playwright", "install", "chromium"], check=True)
        print("✅ Playwright browsers installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")

    # Create .env file if it doesn't exist
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            with open(".env", "w") as f:
                f.write(open(".env.example").read())
            print("📝 Created .env file from .env.example")
        else:
            # Create basic .env file
            env_content = """# Instagram Credentials
INSTA_USERNAME=your_instagram_username
INSTA_PASSWORD=your_instagram_password

# API Keys
ELEVENLABS_API_KEY=your_elevenlabs_api_key
OPENAI_API_KEY=your_openai_api_key

# Optional: For enhanced features
GOOGLE_TRENDS_API_KEY=your_google_trends_api_key
"""
            with open(".env", "w") as f:
                f.write(env_content)
            print("📝 Created basic .env file")

        print("⚠️  Please update .env file with your actual credentials")
    else:
        print("📝 .env file already exists")

    print("\n✨ Setup complete!")
    print("🔧 Next steps:")
    print("   1. Update your .env file with actual credentials")
    print("   2. Run: python main.py")
    print("   3. Check logs/ directory for execution logs")

if __name__ == "__main__":
    setup_project()
