#!/usr/bin/env python3
"""
Environment Setup Script for <PERSON> Quotes Instagram Bot
Helps users configure the bot for local development or production deployment
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies"""
    print("📦 INSTALLING DEPENDENCIES")
    print("=" * 40)
    
    dependencies = [
        "langchain-openai",
        "langchain-community", 
        "crewai",
        "moviepy",
        "opencv-python",
        "pillow",
        "requests",
        "python-dotenv",
        "schedule",
        "instagrapi"
    ]
    
    print("   Installing required packages...")
    for dep in dependencies:
        try:
            print(f"   📦 Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"   ✅ {dep} installed successfully")
        except subprocess.CalledProcessError:
            print(f"   ❌ Failed to install {dep}")
    
    print("   📦 Dependencies installation complete!")

def setup_environment_file():
    """Setup .env file with proper configuration"""
    print("\n🔧 ENVIRONMENT CONFIGURATION")
    print("=" * 40)
    
    env_template = """# Environment Configuration
# Set to 'development' for local testing (runs once and exits)
# Set to 'production' for scheduled posting (runs continuously)
ENVIRONMENT=development

# Instagram Credentials (Required for posting)
INSTA_USERNAME=your_instagram_username
INSTA_PASSWORD=your_instagram_password

# Primary LLM API Keys (Required for CrewAI agents)
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Voice Synthesis (Required for voiceovers)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Content Research APIs (Optional but recommended)
RAPIDAPI_KEY=your_rapidapi_key_here
GOOGLE_TRENDS_API_KEY=your_rapidapi_key_here
NEWSAPI_KEY=your_newsapi_key_here

# Multimedia Generation APIs (Optional but recommended)
PIXABAY_API_KEY=your_pixabay_api_key_here
IMAGINE_TOKEN="Bearer your_imagine_api_token_here"
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w') as f:
            f.write(env_template)
        print("   ✅ .env file created with template")
        print("   💡 Please edit .env file with your actual API keys")
    else:
        print("   ✅ .env file already exists")
        print("   💡 Please verify your API keys are configured")

def create_directories():
    """Create required directories"""
    print("\n📁 CREATING DIRECTORIES")
    print("=" * 30)
    
    directories = [
        "logs",
        "temp", 
        "assets",
        "assets/backgrounds",
        "utils"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}/ directory ready")

def test_installation():
    """Test the installation"""
    print("\n🧪 TESTING INSTALLATION")
    print("=" * 30)
    
    try:
        # Test imports
        print("   📦 Testing imports...")
        
        test_imports = [
            ("crewai", "CrewAI framework"),
            ("moviepy.editor", "Video processing"),
            ("PIL", "Image processing"),
            ("requests", "HTTP requests"),
            ("dotenv", "Environment variables")
        ]
        
        for module, description in test_imports:
            try:
                __import__(module)
                print(f"   ✅ {description}: Available")
            except ImportError:
                print(f"   ❌ {description}: Missing")
        
        # Test environment file
        print("   🔧 Testing environment configuration...")
        if os.path.exists('.env'):
            print("   ✅ .env file exists")
        else:
            print("   ❌ .env file missing")
        
        print("   🧪 Installation test complete!")
        return True
        
    except Exception as e:
        print(f"   ❌ Installation test failed: {str(e)}")
        return False

def show_next_steps():
    """Show next steps to user"""
    print("\n🎯 NEXT STEPS")
    print("=" * 20)
    print()
    print("1. 🔑 CONFIGURE API KEYS:")
    print("   Edit the .env file with your actual API keys:")
    print("   - OpenAI API key (required)")
    print("   - ElevenLabs API key (required)")
    print("   - Instagram credentials (for posting)")
    print("   - Other API keys (optional but recommended)")
    print()
    print("2. 🧪 TEST LOCALLY:")
    print("   python run_local.py")
    print("   (Runs once and exits - perfect for testing)")
    print()
    print("3. 🏭 DEPLOY TO PRODUCTION:")
    print("   python run_production.py")
    print("   (Runs continuously with scheduled posts)")
    print()
    print("4. 📊 MONITOR LOGS:")
    print("   tail -f logs/buddha_bot.log")
    print("   (Watch real-time execution logs)")
    print()
    print("📖 For detailed instructions, see DEPLOYMENT_GUIDE.md")

def main():
    """Main setup function"""
    print("🚀 BUDDHA QUOTES INSTAGRAM BOT SETUP")
    print("=" * 60)
    print("   This script will help you set up the bot for local development")
    print("   or production deployment with proper environment configuration.")
    print()
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    print()
    
    try:
        # Step 1: Install dependencies
        install_dependencies()
        
        # Step 2: Setup environment file
        setup_environment_file()
        
        # Step 3: Create directories
        create_directories()
        
        # Step 4: Test installation
        test_success = test_installation()
        
        # Step 5: Show next steps
        show_next_steps()
        
        if test_success:
            print("\n🎉 SETUP COMPLETE!")
            print("   The Buddha Quotes Instagram Bot is ready for configuration.")
            print("   Please edit the .env file with your API keys and test locally.")
        else:
            print("\n⚠️  SETUP COMPLETED WITH WARNINGS")
            print("   Some components may need manual configuration.")
            print("   Please check the error messages above.")
        
    except KeyboardInterrupt:
        print("\n🛑 Setup interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Setup failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
