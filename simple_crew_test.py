#!/usr/bin/env python3
"""
Simple CrewAI test without timeout mechanisms (Windows compatible)
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_individual_tools():
    """Test individual tools quickly"""
    print("🔧 TESTING INDIVIDUAL TOOLS")
    print("=" * 40)
    
    try:
        # Test QuoteGeneratorTool (fastest)
        print("   📝 Testing QuoteGeneratorTool...")
        from tools.content_tools import QuoteGeneratorTool
        quote_tool = QuoteGeneratorTool()
        result = quote_tool._run("peace")
        print(f"   ✅ Quote: {result[:80]}...")
        
        # Test MidjourneyImageTool (should be fast with fallback)
        print("   🖼️  Testing MidjourneyImageTool...")
        from tools.multimedia_tools import MidjourneyImageTool
        image_tool = MidjourneyImageTool()
        result = image_tool._run("peace", "peaceful", "cinematic")
        print(f"   ✅ Image: {result[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Tool test failed: {str(e)}")
        return False

def test_minimal_crew():
    """Test minimal crew with just quote generation"""
    print("\n🎯 TESTING MINIMAL CREW")
    print("=" * 35)
    
    try:
        from crewai import Agent, Task, Crew, Process
        from tools.content_tools import QuoteGeneratorTool
        
        print("   🏗️  Creating minimal crew...")
        
        # Create simple agent and task
        quote_tool = QuoteGeneratorTool()
        agent = Agent(
            role='Quote Creator',
            goal='Generate a Buddha quote',
            backstory='You create spiritual quotes.',
            tools=[quote_tool],
            verbose=False  # Reduce verbosity
        )
        
        task = Task(
            description='Generate a short Buddha quote about peace.',
            agent=agent,
            expected_output="A short quote"
        )
        
        crew = Crew(
            agents=[agent],
            tasks=[task],
            process=Process.sequential,
            verbose=False  # Reduce verbosity
        )
        
        print("   🚀 Executing minimal crew...")
        print("   ⏳ This may take a moment...")
        
        result = crew.kickoff()
        print("   ✅ Minimal crew successful!")
        print(f"   📊 Result: {str(result)}")
        return True, result
        
    except Exception as e:
        print(f"   ❌ Minimal crew failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def test_crew_creation_only():
    """Test just creating the full crew without executing"""
    print("\n🏗️  TESTING FULL CREW CREATION")
    print("=" * 40)
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        print("   📦 Creating BuddhaQuoteCrew...")
        buddha_crew = BuddhaQuoteCrew()
        
        print("   ⚙️  Creating crew...")
        crew = buddha_crew.create_crew()
        
        print(f"   ✅ Crew created successfully!")
        print(f"   📊 Agents: {len(crew.agents)}")
        print(f"   📊 Tasks: {len(crew.tasks)}")
        
        # Show agent details
        print("   👥 Agents:")
        for i, agent in enumerate(crew.agents):
            print(f"      {i+1}. {agent.role}")
        
        return True, crew
        
    except Exception as e:
        print(f"   ❌ Crew creation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, str(e)

def main():
    """Run simplified tests"""
    print("🧪 SIMPLE CREWAI DIAGNOSTIC")
    print("=" * 40)
    
    # Test 1: Individual tools
    tools_ok = test_individual_tools()
    
    # Test 2: Crew creation
    crew_ok, crew_result = test_crew_creation_only()
    
    # Test 3: Minimal execution (only if tools work)
    if tools_ok:
        minimal_ok, minimal_result = test_minimal_crew()
    else:
        minimal_ok = False
        minimal_result = "Skipped due to tool failures"
    
    # Summary
    print("\n📊 DIAGNOSTIC SUMMARY")
    print("=" * 30)
    print(f"Individual Tools: {'✅ PASS' if tools_ok else '❌ FAIL'}")
    print(f"Crew Creation: {'✅ PASS' if crew_ok else '❌ FAIL'}")
    print(f"Minimal Execution: {'✅ PASS' if minimal_ok else '❌ FAIL'}")
    
    if tools_ok and crew_ok and minimal_ok:
        print("\n🎉 DIAGNOSIS: Basic functionality works!")
        print("💡 The hanging issue is likely due to:")
        print("   - Complex multi-agent interactions")
        print("   - API rate limiting or timeouts")
        print("   - Memory issues with ChromaDB")
        print("\n🔧 RECOMMENDED SOLUTIONS:")
        print("   1. Simplify the crew (fewer agents)")
        print("   2. Add error handling and timeouts")
        print("   3. Disable memory features temporarily")
        print("   4. Run agents sequentially with delays")
    else:
        print("\n❌ DIAGNOSIS: Fundamental issues detected")
        if not tools_ok:
            print("   🔧 Fix tool implementations first")
        if not crew_ok:
            print("   🔧 Fix crew configuration")
        if not minimal_ok:
            print("   🔧 Check OpenAI API connectivity")

if __name__ == "__main__":
    main()
