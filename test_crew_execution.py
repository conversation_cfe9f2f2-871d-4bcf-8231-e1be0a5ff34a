#!/usr/bin/env python3
"""
Test CrewAI execution to identify hanging issues
"""
import os
import sys
import signal
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def timeout_handler(signum, frame):
    raise TimeoutError("CrewAI execution timed out")

def test_crew_execution_with_timeout():
    """Test CrewAI execution with timeout"""
    print("🧪 TESTING CREWAI EXECUTION WITH TIMEOUT")
    print("=" * 50)
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        print("   🏗️  Creating BuddhaQuoteCrew...")
        buddha_crew = BuddhaQuoteCrew()
        
        print("   ⚙️  Creating crew...")
        crew = buddha_crew.create_crew()
        print(f"   ✅ Crew created with {len(crew.agents)} agents")
        
        # Set timeout for execution
        print("   🚀 Starting crew execution with 60-second timeout...")
        
        # Set signal alarm for timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(60)  # 60 second timeout
        
        try:
            result = crew.kickoff()
            signal.alarm(0)  # Cancel alarm
            print("   ✅ Crew execution completed successfully!")
            print(f"   📊 Result: {str(result)[:200]}...")
            return True, result
            
        except TimeoutError:
            print("   ⏰ Crew execution timed out after 60 seconds")
            return False, "Timeout"
            
        except Exception as e:
            signal.alarm(0)  # Cancel alarm
            print(f"   ❌ Crew execution failed: {str(e)}")
            return False, str(e)
            
    except Exception as e:
        print(f"   ❌ Setup failed: {str(e)}")
        return False, str(e)

def test_individual_agent_tools():
    """Test individual agent tools to identify problematic ones"""
    print("\n🔧 TESTING INDIVIDUAL AGENT TOOLS")
    print("=" * 45)
    
    try:
        from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
        from tools.video_tools import VideoCreatorTool
        from tools.multimedia_tools import MidjourneyImageTool, ElevenLabsVoiceTool, PixabayMusicTool
        from tools.instagram_tools import InstagramPostTool
        
        tools_to_test = [
            ("TrendResearchTool", TrendResearchTool(), "mindfulness"),
            ("QuoteGeneratorTool", QuoteGeneratorTool(), "peace"),
            ("SocialMediaTrendsTool", SocialMediaTrendsTool(), "buddha quotes"),
            ("MidjourneyImageTool", MidjourneyImageTool(), "peace"),
            ("ElevenLabsVoiceTool", ElevenLabsVoiceTool(), "Peace comes from within"),
            ("PixabayMusicTool", PixabayMusicTool(), "peaceful"),
        ]
        
        results = {}
        
        for tool_name, tool_instance, test_input in tools_to_test:
            print(f"   🧪 Testing {tool_name}...")
            
            try:
                # Set timeout for each tool
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(30)  # 30 second timeout per tool
                
                if tool_name == "TrendResearchTool":
                    result = tool_instance._run(test_input)
                elif tool_name == "QuoteGeneratorTool":
                    result = tool_instance._run(test_input)
                elif tool_name == "SocialMediaTrendsTool":
                    result = tool_instance._run(test_input)
                elif tool_name == "MidjourneyImageTool":
                    result = tool_instance._run(test_input, "peaceful", "cinematic")
                elif tool_name == "ElevenLabsVoiceTool":
                    result = tool_instance._run(test_input, "grandpa_spuds", "calm", 1.0)
                elif tool_name == "PixabayMusicTool":
                    result = tool_instance._run(test_input, 30, "ambient")
                
                signal.alarm(0)  # Cancel alarm
                print(f"      ✅ Success: {str(result)[:100]}...")
                results[tool_name] = True
                
            except TimeoutError:
                print(f"      ⏰ Timeout after 30 seconds")
                results[tool_name] = False
                
            except Exception as e:
                signal.alarm(0)  # Cancel alarm
                print(f"      ❌ Error: {str(e)}")
                results[tool_name] = False
        
        return results
        
    except Exception as e:
        print(f"   ❌ Setup failed: {str(e)}")
        return {}

def test_minimal_crew():
    """Test a minimal crew with just one agent"""
    print("\n🎯 TESTING MINIMAL CREW")
    print("=" * 35)
    
    try:
        from crewai import Agent, Task, Crew, Process
        from tools.content_tools import QuoteGeneratorTool
        
        print("   🏗️  Creating minimal crew with one agent...")
        
        # Create simple agent
        quote_tool = QuoteGeneratorTool()
        simple_agent = Agent(
            role='Quote Creator',
            goal='Generate a simple Buddha quote',
            backstory='You create simple spiritual quotes.',
            tools=[quote_tool],
            verbose=True
        )
        
        # Create simple task
        simple_task = Task(
            description='Generate a short Buddha-style quote about peace.',
            agent=simple_agent,
            expected_output="A short inspirational quote"
        )
        
        # Create minimal crew
        minimal_crew = Crew(
            agents=[simple_agent],
            tasks=[simple_task],
            process=Process.sequential,
            verbose=True
        )
        
        print("   🚀 Executing minimal crew with 30-second timeout...")
        
        # Set timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)
        
        try:
            result = minimal_crew.kickoff()
            signal.alarm(0)
            print("   ✅ Minimal crew execution successful!")
            print(f"   📊 Result: {str(result)}")
            return True, result
            
        except TimeoutError:
            print("   ⏰ Minimal crew timed out")
            return False, "Timeout"
            
        except Exception as e:
            signal.alarm(0)
            print(f"   ❌ Minimal crew failed: {str(e)}")
            return False, str(e)
            
    except Exception as e:
        print(f"   ❌ Setup failed: {str(e)}")
        return False, str(e)

def main():
    """Run all tests"""
    print("🔍 CREWAI EXECUTION DIAGNOSTIC")
    print("=" * 50)
    
    # Test 1: Individual tools
    tool_results = test_individual_agent_tools()
    
    # Test 2: Minimal crew
    minimal_success, minimal_result = test_minimal_crew()
    
    # Test 3: Full crew (with timeout)
    full_success, full_result = test_crew_execution_with_timeout()
    
    # Summary
    print("\n📊 EXECUTION DIAGNOSTIC SUMMARY")
    print("=" * 45)
    
    print("Individual Tools:")
    for tool, success in tool_results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {tool}: {status}")
    
    print(f"\nMinimal Crew: {'✅ PASS' if minimal_success else '❌ FAIL'}")
    print(f"Full Crew: {'✅ PASS' if full_success else '❌ FAIL'}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 30)
    
    if not all(tool_results.values()):
        failing_tools = [tool for tool, success in tool_results.items() if not success]
        print(f"🔧 Fix failing tools: {', '.join(failing_tools)}")
    
    if not minimal_success:
        print("🤖 Basic CrewAI execution is failing - check OpenAI API")
    elif not full_success:
        print("⚙️  Full crew is too complex or has problematic agents")
        print("   Consider simplifying the crew or adding error handling")
    else:
        print("🎉 All tests passed! The crew should work in production.")

if __name__ == "__main__":
    main()
