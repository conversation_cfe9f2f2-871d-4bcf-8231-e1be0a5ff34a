#!/usr/bin/env python3
"""
Test script for Imagine AI integration
Tests the new ImagineImageTool with the configured API token
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imagine_api_configuration():
    """Test Imagine API configuration"""
    print("🔑 TESTING IMAGINE API CONFIGURATION")
    print("=" * 50)
    
    # Check if token is configured
    api_token = os.getenv("IMAGINE_TOKEN")
    if api_token:
        print(f"   ✅ IMAGINE_TOKEN configured: {api_token[:20]}...")
        return True
    else:
        print("   ❌ IMAGINE_TOKEN not found in environment variables")
        return False

def test_imagine_image_tool():
    """Test the ImagineImageTool"""
    print("\n🎨 TESTING IMAGINE IMAGE TOOL")
    print("=" * 40)
    
    try:
        from tools.multimedia_tools import ImagineImageTool
        
        print("   📦 ImagineImageTool imported successfully")
        
        # Create tool instance
        imagine_tool = ImagineImageTool()
        print("   ✅ Tool instance created")
        
        # Test with different themes and moods
        test_cases = [
            {"theme": "peace", "mood": "peaceful", "style": "cinematic"},
            {"theme": "wisdom", "mood": "zen", "style": "minimalist"},
            {"theme": "meditation", "mood": "meditative", "style": "ethereal"}
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n   🧪 Test Case {i}: {test_case}")
            
            try:
                result = imagine_tool._run(
                    quote_theme=test_case["theme"],
                    mood=test_case["mood"],
                    style=test_case["style"]
                )
                
                print(f"   📊 Result: {result[:100]}...")
                
                # Check if image was created
                if "generated successfully" in result.lower():
                    print("   ✅ Image generation successful")
                elif "fallback" in result.lower():
                    print("   ⚠️  API unavailable, fallback image created")
                else:
                    print("   ❓ Unexpected result")
                    
            except Exception as e:
                print(f"   ❌ Test case failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ImagineImageTool test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_generation():
    """Test prompt generation functionality"""
    print("\n📝 TESTING PROMPT GENERATION")
    print("=" * 40)
    
    try:
        from tools.multimedia_tools import ImagineImageTool
        
        imagine_tool = ImagineImageTool()
        
        # Test prompt creation
        test_prompts = [
            {"theme": "inner peace", "mood": "peaceful", "style": "cinematic"},
            {"theme": "spiritual awakening", "mood": "inspiring", "style": "ethereal"},
            {"theme": "mindfulness", "mood": "zen", "style": "minimalist"}
        ]
        
        for i, test in enumerate(test_prompts, 1):
            print(f"\n   🎯 Prompt Test {i}:")
            print(f"      Theme: {test['theme']}")
            print(f"      Mood: {test['mood']}")
            print(f"      Style: {test['style']}")
            
            try:
                prompt = imagine_tool._create_imagine_prompt(
                    test["theme"], test["mood"], test["style"]
                )
                
                print(f"   📝 Generated Prompt:")
                print(f"      {prompt[:150]}...")
                
                # Check prompt quality
                quality_indicators = [
                    "cinematic", "lighting", "composition", "9:16", "vertical",
                    "professional", "high quality", "no text", "no watermark"
                ]
                
                found_indicators = [ind for ind in quality_indicators if ind in prompt.lower()]
                print(f"   📊 Quality indicators found: {len(found_indicators)}/9")
                
                if len(found_indicators) >= 6:
                    print("   ✅ High-quality prompt generated")
                else:
                    print("   ⚠️  Prompt could be improved")
                    
            except Exception as e:
                print(f"   ❌ Prompt generation failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Prompt generation test failed: {str(e)}")
        return False

def test_fallback_functionality():
    """Test fallback image creation"""
    print("\n🛡️  TESTING FALLBACK FUNCTIONALITY")
    print("=" * 45)
    
    try:
        from tools.multimedia_tools import ImagineImageTool
        
        imagine_tool = ImagineImageTool()
        
        # Test fallback image creation
        print("   🎨 Testing fallback image creation...")
        
        result = imagine_tool._create_fallback_image("peace", "peaceful", "cinematic")
        print(f"   📊 Fallback result: {result[:100]}...")
        
        if "fallback image created" in result.lower():
            print("   ✅ Fallback functionality working")
            
            # Check if file was actually created
            if "assets/backgrounds/" in result:
                filename = result.split("Fallback image created: ")[1].split(" |")[0]
                if os.path.exists(filename):
                    print(f"   ✅ Fallback image file exists: {filename}")
                    
                    # Check file size
                    file_size = os.path.getsize(filename)
                    print(f"   📊 File size: {file_size} bytes")
                    
                    if file_size > 10000:  # At least 10KB
                        print("   ✅ File size looks good")
                    else:
                        print("   ⚠️  File size seems small")
                else:
                    print("   ❌ Fallback image file not found")
        else:
            print("   ❌ Fallback functionality failed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Fallback test failed: {str(e)}")
        return False

def test_crew_integration():
    """Test integration with CrewAI agents"""
    print("\n🤖 TESTING CREWAI INTEGRATION")
    print("=" * 40)
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        print("   📦 BuddhaQuoteCrew imported successfully")
        
        # Create crew instance
        buddha_crew = BuddhaQuoteCrew()
        print("   ✅ Crew instance created")
        
        # Create crew
        crew = buddha_crew.create_crew()
        if crew:
            print(f"   ✅ Crew created with {len(crew.agents)} agents")
            
            # Check if Image Generation Specialist exists
            image_agent = None
            for agent in crew.agents:
                if "image generation" in agent.role.lower():
                    image_agent = agent
                    break
            
            if image_agent:
                print(f"   ✅ Image Generation Specialist found: {image_agent.role}")
                
                # Check if agent has the right tools
                if hasattr(image_agent, 'tools') and image_agent.tools:
                    tool_names = [tool.name for tool in image_agent.tools]
                    print(f"   📊 Agent tools: {tool_names}")
                    
                    if any("imagine" in name.lower() for name in tool_names):
                        print("   ✅ Imagine AI tool properly integrated")
                    else:
                        print("   ⚠️  Imagine AI tool not found in agent tools")
                else:
                    print("   ❌ Agent has no tools configured")
            else:
                print("   ❌ Image Generation Specialist not found")
        else:
            print("   ❌ Failed to create crew")
        
        return crew is not None
        
    except Exception as e:
        print(f"   ❌ CrewAI integration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Imagine AI tests"""
    print("🚀 IMAGINE AI INTEGRATION TEST SUITE")
    print("=" * 60)
    
    # Ensure directories exist
    os.makedirs('assets/backgrounds', exist_ok=True)
    
    # Run tests
    test_results = {}
    
    test_results['api_config'] = test_imagine_api_configuration()
    test_results['image_tool'] = test_imagine_image_tool()
    test_results['prompt_gen'] = test_prompt_generation()
    test_results['fallback'] = test_fallback_functionality()
    test_results['crew_integration'] = test_crew_integration()
    
    # Summary
    print("\n📊 IMAGINE AI TEST SUMMARY")
    print("=" * 40)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    overall_success = all(test_results.values())
    print(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if overall_success else '⚠️  SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n💡 Imagine AI integration is ready for production!")
        print("   - API token configured correctly")
        print("   - Image generation working with fallback")
        print("   - CrewAI agents properly integrated")
        print("   - Professional prompt generation active")
    else:
        print("\n🔧 Issues detected:")
        if not test_results['api_config']:
            print("   - Configure IMAGINE_TOKEN in .env file")
        if not test_results['image_tool']:
            print("   - Check ImagineImageTool implementation")
        if not test_results['crew_integration']:
            print("   - Verify CrewAI agent configuration")

if __name__ == "__main__":
    main()
