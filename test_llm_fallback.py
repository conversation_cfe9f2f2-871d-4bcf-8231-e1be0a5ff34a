#!/usr/bin/env python3
"""
Test script for LLM fallback mechanism
Tests automatic switching from OpenAI to DeepSeek when rate limits are encountered
"""

import os
import sys
import time
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_test_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/fallback_test.log')
        ]
    )
    return logging.getLogger('FallbackTest')

def test_llm_fallback_manager():
    """Test the LLM fallback manager"""
    logger = setup_test_logging()
    logger.info("🧪 TESTING LLM FALLBACK MANAGER")
    logger.info("=" * 50)
    
    try:
        from utils.llm_fallback_manager import LLMFallbackManager, LLMProvider, RateLimitError
        
        # Test 1: Initialize manager
        logger.info("   📦 Initializing LLM Fallback Manager...")
        manager = LLMFallbackManager(logger)
        
        # Test 2: Check configurations
        logger.info("   🔧 Checking provider configurations...")
        stats = manager.get_usage_stats()
        logger.info(f"   📊 Available providers: {[p.value for p in stats['available_providers']]}")
        logger.info(f"   📊 Current provider: {stats['current_provider']}")
        logger.info(f"   📊 Fallback provider: {stats['fallback_provider']}")
        
        # Test 3: Test rate limit detection
        logger.info("   🔍 Testing rate limit error detection...")
        test_errors = [
            Exception("Rate limit reached for gpt-4o-mini"),
            Exception("HTTP 429: Too many requests"),
            Exception("quota exceeded"),
            Exception("Some other error")
        ]
        
        for error in test_errors:
            is_rate_limit = manager.is_rate_limit_error(error)
            logger.info(f"      Error: '{str(error)[:50]}...' -> Rate limit: {is_rate_limit}")
        
        # Test 4: Test fallback switching
        logger.info("   🔄 Testing fallback switching...")
        original_provider = manager.current_provider
        
        # Simulate rate limit error
        fake_rate_limit_error = RateLimitError("Rate limit exceeded")
        fallback_attempted, message = manager.handle_llm_error(fake_rate_limit_error)
        
        logger.info(f"   📊 Fallback attempted: {fallback_attempted}")
        logger.info(f"   📊 Message: {message}")
        
        if fallback_attempted:
            logger.info(f"   ✅ Successfully switched from {original_provider.value} to {manager.current_provider.value}")
        else:
            logger.info(f"   ⚠️  Fallback not available or conditions not met")
        
        return True
        
    except Exception as e:
        logger.error(f"   ❌ LLM Fallback Manager test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_crewai_config_manager():
    """Test the CrewAI configuration manager"""
    logger = setup_test_logging()
    logger.info("\n🤖 TESTING CREWAI CONFIG MANAGER")
    logger.info("=" * 50)
    
    try:
        from utils.crewai_config_manager import CrewAIConfigManager
        
        # Test 1: Initialize manager
        logger.info("   📦 Initializing CrewAI Config Manager...")
        config_manager = CrewAIConfigManager(logger)
        
        # Test 2: Check provider info
        logger.info("   📊 Checking provider information...")
        provider_info = config_manager.get_current_provider_info()
        logger.info(f"   📊 Current provider: {provider_info['current_provider']}")
        logger.info(f"   📊 LLM available: {provider_info['llm_available']}")
        
        # Test 3: Test agent creation
        logger.info("   👤 Testing agent creation...")
        test_agent = config_manager.create_agent_with_current_llm(
            role='Test Agent',
            goal='Test goal',
            backstory='Test backstory',
            tools=[],
            verbose=False
        )
        
        if test_agent:
            logger.info(f"   ✅ Successfully created test agent: {test_agent.role}")
        else:
            logger.info("   ❌ Failed to create test agent")
        
        return test_agent is not None
        
    except Exception as e:
        logger.error(f"   ❌ CrewAI Config Manager test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_buddha_crew_with_fallback():
    """Test the Buddha crew with fallback support"""
    logger = setup_test_logging()
    logger.info("\n🧘 TESTING BUDDHA CREW WITH FALLBACK")
    logger.info("=" * 50)
    
    try:
        from agents.crew_agents import BuddhaQuoteCrew
        
        # Test 1: Create crew
        logger.info("   🏗️  Creating Buddha Quote Crew...")
        buddha_crew = BuddhaQuoteCrew()
        
        # Test 2: Create crew with fallback support
        logger.info("   ⚙️  Creating crew with fallback support...")
        crew = buddha_crew.create_crew()
        
        if crew:
            logger.info(f"   ✅ Successfully created crew with {len(crew.agents)} agents")
            logger.info(f"   📊 Tasks: {len(crew.tasks)}")
            
            # Test agent LLM configuration
            logger.info("   🔍 Checking agent LLM configurations...")
            for i, agent in enumerate(crew.agents):
                has_llm = hasattr(agent, 'llm') and agent.llm is not None
                logger.info(f"      Agent {i+1} ({agent.role}): LLM configured = {has_llm}")
            
            return True
        else:
            logger.info("   ❌ Failed to create crew")
            return False
        
    except Exception as e:
        logger.error(f"   ❌ Buddha Crew test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_configuration():
    """Test environment configuration for fallback"""
    logger = setup_test_logging()
    logger.info("\n🔑 TESTING ENVIRONMENT CONFIGURATION")
    logger.info("=" * 50)
    
    required_vars = {
        'OPENAI_API_KEY': 'Primary LLM provider',
        'DEEPSEEK_API_KEY': 'Fallback LLM provider'
    }
    
    optional_vars = {
        'ELEVENLABS_API_KEY': 'Voice synthesis',
        'RAPIDAPI_KEY': 'Content research',
        'MIDJOURNEY_API_KEY': 'Image generation',
        'PIXABAY_API_KEY': 'Music curation'
    }
    
    config_status = {}
    
    logger.info("   🔍 Checking required API keys...")
    for var, description in required_vars.items():
        value = os.getenv(var)
        is_configured = value and value != f'your_{var.lower()}'
        config_status[var] = is_configured
        status = "✅ CONFIGURED" if is_configured else "❌ MISSING"
        logger.info(f"      {var} ({description}): {status}")
    
    logger.info("   🔍 Checking optional API keys...")
    for var, description in optional_vars.items():
        value = os.getenv(var)
        is_configured = value and value != f'your_{var.lower()}'
        config_status[var] = is_configured
        status = "✅ CONFIGURED" if is_configured else "⚠️  NOT SET"
        logger.info(f"      {var} ({description}): {status}")
    
    # Check if fallback is possible
    fallback_possible = config_status.get('OPENAI_API_KEY', False) and config_status.get('DEEPSEEK_API_KEY', False)
    logger.info(f"\n   📊 Fallback mechanism possible: {'✅ YES' if fallback_possible else '❌ NO'}")
    
    return fallback_possible

def simulate_rate_limit_scenario():
    """Simulate a rate limit scenario and test fallback"""
    logger = setup_test_logging()
    logger.info("\n⚡ SIMULATING RATE LIMIT SCENARIO")
    logger.info("=" * 50)
    
    try:
        from utils.llm_fallback_manager import LLMFallbackManager, RateLimitError
        
        logger.info("   🎭 Creating simulated rate limit scenario...")
        manager = LLMFallbackManager(logger)
        
        # Record initial state
        initial_provider = manager.current_provider
        logger.info(f"   📊 Initial provider: {initial_provider.value}")
        
        # Simulate multiple rate limit errors
        logger.info("   🔥 Simulating rate limit errors...")
        for i in range(3):
            logger.info(f"      Attempt {i+1}: Simulating rate limit...")
            
            # Create a realistic rate limit error
            rate_limit_error = Exception("Rate limit reached for gpt-4o-mini in organization org-xyz on requests per min (RPM): Limit 3, Used 3, Requested 1")
            
            fallback_attempted, message = manager.handle_llm_error(rate_limit_error)
            logger.info(f"      Result: {message}")
            
            if fallback_attempted:
                logger.info(f"      ✅ Switched to: {manager.current_provider.value}")
                break
            else:
                logger.info(f"      ⏳ Waiting before next attempt...")
                time.sleep(1)  # Short wait for testing
        
        # Check final state
        final_provider = manager.current_provider
        logger.info(f"   📊 Final provider: {final_provider.value}")
        
        # Get usage statistics
        stats = manager.get_usage_stats()
        logger.info("   📊 Usage Statistics:")
        for provider, provider_stats in stats['stats'].items():
            logger.info(f"      {provider}: {provider_stats}")
        
        return initial_provider != final_provider
        
    except Exception as e:
        logger.error(f"   ❌ Rate limit simulation failed: {str(e)}")
        return False

def main():
    """Run all fallback tests"""
    logger = setup_test_logging()
    logger.info("🚀 LLM FALLBACK MECHANISM TEST SUITE")
    logger.info("=" * 60)
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Run tests
    test_results = {}
    
    test_results['environment'] = test_environment_configuration()
    test_results['llm_manager'] = test_llm_fallback_manager()
    test_results['crewai_manager'] = test_crewai_config_manager()
    test_results['buddha_crew'] = test_buddha_crew_with_fallback()
    test_results['rate_limit_sim'] = simulate_rate_limit_scenario()
    
    # Summary
    logger.info("\n📊 FALLBACK TEST SUMMARY")
    logger.info("=" * 40)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
    
    overall_success = all(test_results.values())
    logger.info(f"\nOverall Result: {'🎉 ALL TESTS PASSED' if overall_success else '⚠️  SOME TESTS FAILED'}")
    
    if overall_success:
        logger.info("\n💡 The LLM fallback mechanism is ready for production!")
        logger.info("   - Automatic switching from OpenAI to DeepSeek on rate limits")
        logger.info("   - Seamless integration with CrewAI agents")
        logger.info("   - Comprehensive error handling and logging")
    else:
        logger.info("\n🔧 Issues detected - check the logs above for details")
        logger.info("   - Ensure both OpenAI and DeepSeek API keys are configured")
        logger.info("   - Verify all required dependencies are installed")

if __name__ == "__main__":
    main()
