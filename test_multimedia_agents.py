#!/usr/bin/env python3
"""
Test script for the new specialized multimedia CrewAI agents
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_multimedia_tools():
    """Test individual multimedia tools"""
    print("🎬 TESTING MULTIMEDIA TOOLS")
    print("=" * 50)
    
    try:
        from tools.multimedia_tools import (
            MidjourneyImageTool, ElevenLabsVoiceTool, PixabayMusicTool
        )
        from tools.cinematic_video_tools import CinematicVideoTool
        
        print("✅ All multimedia tools imported successfully")
        
        # Test quote for all tools
        test_quote = "Peace comes from within. Do not seek it without."
        test_theme = "peace"
        
        # Test 1: Midjourney Image Tool
        print("\n🖼️  Testing Midjourney Image Tool...")
        midjourney_tool = MidjourneyImageTool()
        image_result = midjourney_tool._run(test_theme, mood="peaceful", style="cinematic")
        print(f"   Result: {image_result}")
        
        # Test 2: ElevenLabs Voice Tool
        print("\n🎤 Testing ElevenLabs Voice Tool...")
        voice_tool = ElevenLabsVoiceTool()
        voice_result = voice_tool._run(test_quote, emotion="calm", speed=1.0)
        print(f"   Result: {voice_result}")
        
        # Test 3: Pixabay Music Tool
        print("\n🎵 Testing Pixabay Music Tool...")
        music_tool = PixabayMusicTool()
        music_result = music_tool._run(test_theme, duration=30, genre="ambient")
        print(f"   Result: {music_result}")
        
        # Test 4: Cinematic Video Tool
        print("\n🎬 Testing Cinematic Video Tool...")
        video_tool = CinematicVideoTool()
        
        # Create dummy file paths for testing
        dummy_image = "assets/backgrounds/test_image.jpg"
        dummy_audio = "temp/test_audio.mp3"
        dummy_music = "temp/test_music.mp3"
        
        video_result = video_tool._run(
            quote_text=test_quote,
            background_image=dummy_image,
            voiceover_audio=dummy_audio,
            background_music=dummy_music,
            video_length=30,
            style="cinematic"
        )
        print(f"   Result: {video_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing multimedia tools: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multimedia_agents():
    """Test the specialized multimedia agents"""
    print("\n🤖 TESTING MULTIMEDIA AGENTS")
    print("=" * 50)
    
    try:
        from agents.multimedia_agents import MultimediaContentCrew
        
        print("✅ MultimediaContentCrew imported successfully")
        
        # Create multimedia crew
        multimedia_crew = MultimediaContentCrew()
        print("✅ MultimediaContentCrew initialized")
        
        # Test agent creation
        print("\n👥 Testing agent creation...")
        
        # Test AI Image Specialist
        image_agent = multimedia_crew.ai_image_specialist
        print(f"   ✅ AI Image Specialist: {image_agent.role}")
        
        # Test Voice Synthesis Specialist
        voice_agent = multimedia_crew.voice_synthesis_specialist
        print(f"   ✅ Voice Synthesis Specialist: {voice_agent.role}")
        
        # Test Music Curation Specialist
        music_agent = multimedia_crew.music_curation_specialist
        print(f"   ✅ Music Curation Specialist: {music_agent.role}")
        
        # Test Cinematic Video Specialist
        video_agent = multimedia_crew.cinematic_video_specialist
        print(f"   ✅ Cinematic Video Specialist: {video_agent.role}")
        
        # Test crew creation
        print("\n🎬 Testing crew creation...")
        crew = multimedia_crew.create_multimedia_crew()
        print(f"   ✅ Multimedia crew created with {len(crew.agents)} agents")
        print(f"   ✅ Crew has {len(crew.tasks)} coordinated tasks")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing multimedia agents: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """Test required dependencies for multimedia functionality"""
    print("\n📦 TESTING DEPENDENCIES")
    print("=" * 40)
    
    dependencies = {
        "PIL (Pillow)": "PIL",
        "NumPy": "numpy", 
        "Requests": "requests",
        "MoviePy": "moviepy.editor",
        "OpenCV": "cv2"
    }
    
    results = {}
    
    for name, module in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {name}: Available")
            results[name] = True
        except ImportError:
            print(f"   ❌ {name}: Not available")
            results[name] = False
    
    # Check for optional dependencies
    print("\n📋 Optional Dependencies:")
    optional_deps = {
        "CrewAI": "crewai",
        "Pydantic": "pydantic"
    }
    
    for name, module in optional_deps.items():
        try:
            __import__(module)
            print(f"   ✅ {name}: Available")
            results[name] = True
        except ImportError:
            print(f"   ⚠️  {name}: Not available (required for full functionality)")
            results[name] = False
    
    return results

def test_api_configuration():
    """Test API key configuration"""
    print("\n🔑 TESTING API CONFIGURATION")
    print("=" * 45)
    
    api_keys = {
        "ElevenLabs": "ELEVENLABS_API_KEY",
        "OpenAI": "OPENAI_API_KEY", 
        "Midjourney": "MIDJOURNEY_API_KEY",
        "Pixabay": "PIXABAY_API_KEY",
        "RapidAPI": "RAPIDAPI_KEY"
    }
    
    configured_apis = 0
    
    for service, env_var in api_keys.items():
        key = os.getenv(env_var)
        if key and key != f'your_{env_var.lower()}':
            print(f"   ✅ {service}: Configured")
            configured_apis += 1
        else:
            print(f"   ⚠️  {service}: Not configured")
    
    print(f"\n📊 API Configuration Summary: {configured_apis}/{len(api_keys)} services configured")
    
    return configured_apis > 0

def create_installation_guide():
    """Create installation guide for missing dependencies"""
    print("\n📝 CREATING INSTALLATION GUIDE")
    print("=" * 45)
    
    guide_content = """
# Multimedia Agents Installation Guide

## Required Dependencies

Install the following packages for full multimedia functionality:

```bash
# Core video processing
pip install moviepy opencv-python

# Image processing and utilities  
pip install pillow numpy requests

# CrewAI and AI framework
pip install crewai pydantic

# Optional: For advanced audio processing
pip install librosa soundfile
```

## API Keys Setup

Add the following to your .env file:

```bash
# Required for voice synthesis
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Required for AI agents
OPENAI_API_KEY=your_openai_api_key

# Optional: For enhanced functionality
MIDJOURNEY_API_KEY=your_midjourney_api_key
PIXABAY_API_KEY=your_pixabay_api_key
```

## Getting API Keys

1. **ElevenLabs**: Sign up at https://elevenlabs.io
2. **OpenAI**: Get API key from https://platform.openai.com
3. **Pixabay**: Free API at https://pixabay.com/api/docs/
4. **Midjourney**: Currently requires Discord bot setup

## Troubleshooting

- If MoviePy fails, try: `pip install moviepy[optional]`
- For OpenCV issues on Windows: `pip install opencv-python-headless`
- For audio issues: Install system audio codecs (ffmpeg)

## Testing

Run this test script to verify installation:
```bash
python test_multimedia_agents.py
```
"""
    
    with open("MULTIMEDIA_INSTALLATION.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Installation guide created: MULTIMEDIA_INSTALLATION.md")

if __name__ == "__main__":
    print("🚀 MULTIMEDIA AGENTS TEST SUITE")
    print("=" * 60)
    
    # Test dependencies first
    deps_results = test_dependencies()
    
    # Test API configuration
    api_configured = test_api_configuration()
    
    # Test multimedia tools
    tools_success = test_multimedia_tools()
    
    # Test multimedia agents
    agents_success = test_multimedia_agents()
    
    # Create installation guide
    create_installation_guide()
    
    print("\n📋 FINAL SUMMARY")
    print("=" * 40)
    print(f"Dependencies: {'✅ GOOD' if all(deps_results.values()) else '⚠️  PARTIAL'}")
    print(f"API Configuration: {'✅ CONFIGURED' if api_configured else '⚠️  NEEDS SETUP'}")
    print(f"Multimedia Tools: {'✅ PASSED' if tools_success else '❌ FAILED'}")
    print(f"Multimedia Agents: {'✅ PASSED' if agents_success else '❌ FAILED'}")
    
    if all([tools_success, agents_success]):
        print("\n🎉 All multimedia functionality is ready!")
        print("💡 The specialized agents can now create professional cinematic videos.")
    else:
        print("\n⚠️  Some components need attention. Check the installation guide.")
        print("📖 See MULTIMEDIA_INSTALLATION.md for setup instructions.")
