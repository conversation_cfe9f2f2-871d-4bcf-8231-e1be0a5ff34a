#!/usr/bin/env python3
"""
Test script for RapidAPI integration
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_rapidapi_tools():
    """Test all RapidAPI-enabled tools"""
    print("🧪 TESTING RAPIDAPI INTEGRATION")
    print("=" * 50)
    
    try:
        from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool
        
        # Check API key
        rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
        if rapidapi_key:
            print(f"✅ RapidAPI Key found: {rapidapi_key[:10]}...")
        else:
            print("❌ No RapidAPI key found")
            return False
        
        print("\n📊 Testing Trend Research Tool...")
        trend_tool = TrendResearchTool()
        trend_result = trend_tool._run(["mindfulness", "meditation"])
        print(f"Result: {trend_result}")
        
        print("\n💬 Testing Quote Generator Tool...")
        quote_tool = QuoteGeneratorTool()
        quote_result = quote_tool._run("peace")
        print(f"Result: {quote_result}")
        
        print("\n📱 Testing Social Media Trends Tool...")
        social_tool = SocialMediaTrendsTool()
        social_result = social_tool._run("instagram")
        print(f"Result: {social_result}")
        
        print("\n🎉 All RapidAPI tools tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing RapidAPI tools: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test direct API calls to verify endpoints"""
    print("\n🔗 TESTING DIRECT API ENDPOINTS")
    print("=" * 50)
    
    import requests
    
    rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
    if not rapidapi_key:
        print("❌ No API key available for testing")
        return False
    
    # Test 1: Google Trends API
    print("\n1. Testing Google Trends API...")
    try:
        url = "https://google-trends9.p.rapidapi.com/trending"
        headers = {
            "X-RapidAPI-Key": rapidapi_key,
            "X-RapidAPI-Host": "google-trends9.p.rapidapi.com"
        }
        querystring = {"geo": "US", "hl": "en"}
        
        response = requests.get(url, headers=headers, params=querystring, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Google Trends API working")
        else:
            print(f"   ⚠️  Google Trends API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Google Trends API error: {str(e)}")
    
    # Test 2: Quotes API
    print("\n2. Testing Quotes API...")
    try:
        url = "https://quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com/quote"
        headers = {
            "X-RapidAPI-Key": rapidapi_key,
            "X-RapidAPI-Host": "quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com"
        }
        querystring = {"token": "ihj1QfArLx", "cat": "motivational"}
        
        response = requests.get(url, headers=headers, params=querystring, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Quotes API working")
        else:
            print(f"   ⚠️  Quotes API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Quotes API error: {str(e)}")
    
    # Test 3: Social Media Trends API
    print("\n3. Testing Social Media Trends API...")
    try:
        url = "https://twitter-trends8.p.rapidapi.com/trends24"
        headers = {
            "X-RapidAPI-Key": rapidapi_key,
            "X-RapidAPI-Host": "twitter-trends8.p.rapidapi.com"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Social Media Trends API working")
        else:
            print(f"   ⚠️  Social Media Trends API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Social Media Trends API error: {str(e)}")
    
    return True

if __name__ == "__main__":
    print("🚀 RAPIDAPI INTEGRATION TEST")
    print("=" * 60)
    
    # Test tools
    tools_success = test_rapidapi_tools()
    
    # Test direct endpoints
    endpoints_success = test_api_endpoints()
    
    print("\n📋 SUMMARY")
    print("=" * 30)
    print(f"Tools Test: {'✅ PASSED' if tools_success else '❌ FAILED'}")
    print(f"Endpoints Test: {'✅ PASSED' if endpoints_success else '❌ FAILED'}")
    
    if tools_success and endpoints_success:
        print("\n🎉 All tests passed! RapidAPI integration is working.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
