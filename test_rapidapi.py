#!/usr/bin/env python3
"""
Test script for Free API integration (with RapidAPI fallback)
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_free_api_tools():
    """Test all Free API-enabled tools"""
    print("🧪 TESTING FREE API INTEGRATION")
    print("=" * 50)

    try:
        from tools.content_tools import TrendResearchTool, QuoteGeneratorTool, SocialMediaTrendsTool

        # Check API key
        rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
        if rapidapi_key:
            print(f"✅ RapidAPI Key found: {rapidapi_key[:10]}...")
        else:
            print("❌ No RapidAPI key found")
            return False

        print("\n📊 Testing Trend Research Tool...")
        trend_tool = TrendResearchTool()
        trend_result = trend_tool._run(["mindfulness", "meditation"])
        print(f"Result: {trend_result}")

        print("\n💬 Testing Quote Generator Tool...")
        quote_tool = QuoteGeneratorTool()
        quote_result = quote_tool._run("peace")
        print(f"Result: {quote_result}")

        print("\n📱 Testing Social Media Trends Tool...")
        social_tool = SocialMediaTrendsTool()
        social_result = social_tool._run("instagram")
        print(f"Result: {social_result}")

        print("\n🎉 All Free API tools tested successfully!")
        return True

    except Exception as e:
        print(f"❌ Error testing Free API tools: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_free_api_endpoints():
    """Test direct free API calls to verify endpoints"""
    print("\n🔗 TESTING FREE API ENDPOINTS")
    print("=" * 50)

    import requests

    # Test 1: Quotable.io API (Free)
    print("\n1. Testing Quotable.io API (Free)...")
    try:
        url = "https://api.quotable.io/random"
        params = {"tags": "inspirational|motivational|wisdom", "maxLength": 150}

        response = requests.get(url, params=params, timeout=10, verify=False)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Quotable.io API working")
            print(f"   Sample quote: {data.get('content', 'N/A')[:50]}...")
        else:
            print(f"   ⚠️  Quotable.io API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Quotable.io API error: {str(e)}")

    # Test 2: ZenQuotes API (Free)
    print("\n2. Testing ZenQuotes API (Free)...")
    try:
        url = "https://zenquotes.io/api/random"
        response = requests.get(url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ ZenQuotes API working")
            if isinstance(data, list) and len(data) > 0:
                print(f"   Sample quote: {data[0].get('q', 'N/A')[:50]}...")
        else:
            print(f"   ⚠️  ZenQuotes API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ ZenQuotes API error: {str(e)}")

    # Test 3: Reddit API (Free, no auth needed)
    print("\n3. Testing Reddit API (Free)...")
    try:
        url = "https://www.reddit.com/r/getmotivated/hot.json?limit=3"
        headers = {'User-Agent': 'BuddhaBot/1.0'}
        response = requests.get(url, headers=headers, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Reddit API working")
            if 'data' in data and 'children' in data['data']:
                print(f"   Found {len(data['data']['children'])} posts")
        else:
            print(f"   ⚠️  Reddit API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Reddit API error: {str(e)}")

    # Test 4: HackerNews API (Free)
    print("\n4. Testing HackerNews API (Free)...")
    try:
        url = "https://hacker-news.firebaseio.com/v0/topstories.json"
        response = requests.get(url, timeout=10)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ HackerNews API working")
            print(f"   Found {len(data)} top stories")
        else:
            print(f"   ⚠️  HackerNews API returned: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ HackerNews API error: {str(e)}")

    # Test 5: NewsAPI (if key available)
    newsapi_key = os.getenv('NEWSAPI_KEY')
    if newsapi_key and newsapi_key != 'your_newsapi_key_here':
        print("\n5. Testing NewsAPI...")
        try:
            url = "https://newsapi.org/v2/top-headlines"
            params = {"apiKey": newsapi_key, "country": "us", "category": "health", "pageSize": 3}

            response = requests.get(url, params=params, timeout=10)
            print(f"   Status Code: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print("   ✅ NewsAPI working")
                if 'articles' in data:
                    print(f"   Found {len(data['articles'])} articles")
            else:
                print(f"   ⚠️  NewsAPI returned: {response.text[:100]}")
        except Exception as e:
            print(f"   ❌ NewsAPI error: {str(e)}")
    else:
        print("\n5. NewsAPI key not configured - skipping test")

    return True

if __name__ == "__main__":
    print("🚀 FREE API INTEGRATION TEST")
    print("=" * 60)

    # Test tools
    tools_success = test_free_api_tools()

    # Test direct endpoints
    endpoints_success = test_free_api_endpoints()

    print("\n📋 SUMMARY")
    print("=" * 30)
    print(f"Tools Test: {'✅ PASSED' if tools_success else '❌ FAILED'}")
    print(f"Endpoints Test: {'✅ PASSED' if endpoints_success else '❌ FAILED'}")

    if tools_success and endpoints_success:
        print("\n🎉 All tests passed! Free API integration is working.")
        print("💡 The bot now uses free APIs with RapidAPI as fallback.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
