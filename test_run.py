#!/usr/bin/env python3
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🌟 BUDDHA QUOTES INSTAGRAM BOT - TEST RUN")
print("=" * 60)
print(f"⏰ Starting test at {datetime.now()}")

try:
    print("📦 Testing imports...")
    from agents.crew_agents import BuddhaQuoteCrew
    from tools.content_tools import TrendResearchTool, QuoteGeneratorTool
    from tools.video_tools import VideoCreatorTool
    from tools.instagram_tools import InstagramPostTool
    print("✅ All imports successful!")
    
    print("\n🔧 Testing tool initialization...")
    trend_tool = TrendResearchTool()
    quote_tool = QuoteGeneratorTool()
    video_tool = VideoCreatorTool()
    instagram_tool = InstagramPostTool()
    print("✅ All tools initialized!")
    
    print("\n🤖 Testing CrewAI initialization...")
    buddha_crew = BuddhaQuoteCrew()
    crew = buddha_crew.create_crew()
    print(f"✅ CrewAI initialized with {len(crew.agents)} agents!")
    
    print("\n🧪 Testing individual tools...")
    
    # Test quote generator
    print("   📝 Testing quote generator...")
    quote_result = quote_tool._run("peace")
    print(f"   ✅ Quote generated: {quote_result[:50]}...")
    
    # Test trend research (might fail without API key, that's ok)
    print("   📊 Testing trend research...")
    try:
        trend_result = trend_tool._run(["mindfulness", "peace"])
        print(f"   ✅ Trends researched: {trend_result[:50]}...")
    except Exception as e:
        print(f"   ⚠️  Trend research failed (expected): {str(e)[:50]}...")
    
    print("\n🎉 All basic tests passed!")
    print("💡 The bot is ready to run. Note: Full pipeline requires API keys.")
    
except Exception as e:
    print(f"❌ Test failed: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
