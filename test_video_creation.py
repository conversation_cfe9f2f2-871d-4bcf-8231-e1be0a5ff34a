#!/usr/bin/env python3
"""
Test video creation without ImageMagick
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_video_creation():
    """Test video creation functionality"""
    print("🎬 TESTING VIDEO CREATION (NO IMAGEMAGICK)")
    print("=" * 50)
    
    try:
        from tools.video_tools import VideoCreatorTool
        
        print("✅ VideoCreatorTool imported successfully")
        
        # Create video tool instance
        video_tool = VideoCreatorTool()
        print("✅ VideoCreatorTool initialized")
        
        # Test quote
        test_quote = "Peace comes from within. Do not seek it without."
        
        print(f"\n🎯 Testing video creation with quote:")
        print(f"   '{test_quote}'")
        
        # Create video
        result = video_tool._run(test_quote, duration=5)
        
        print(f"\n📊 Result: {result}")
        
        # Check if file was created
        if "temp/" in result and os.path.exists(result.split(":")[-1].strip()):
            file_path = result.split(":")[-1].strip()
            file_size = os.path.getsize(file_path)
            print(f"✅ Video file created successfully!")
            print(f"   📁 Path: {file_path}")
            print(f"   📏 Size: {file_size} bytes")
        elif "temp/" in result:
            print(f"✅ Output file created: {result}")
        else:
            print(f"⚠️  Unexpected result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing video creation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pil_functionality():
    """Test PIL text rendering functionality"""
    print("\n🖼️  TESTING PIL TEXT RENDERING")
    print("=" * 40)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        print("✅ PIL imported successfully")
        
        # Create test image
        img = Image.new('RGB', (400, 300), color=(139, 69, 19))
        draw = ImageDraw.Draw(img)
        
        # Test text rendering
        test_text = "Test Quote"
        
        try:
            font = ImageFont.truetype("arial.ttf", 30)
            print("✅ Arial font loaded")
        except:
            font = ImageFont.load_default()
            print("⚠️  Using default font (Arial not found)")
        
        # Draw text
        draw.text((50, 150), test_text, font=font, fill=(255, 255, 255))
        
        # Save test image
        test_image_path = "temp/test_text_image.png"
        os.makedirs("temp", exist_ok=True)
        img.save(test_image_path)
        
        if os.path.exists(test_image_path):
            file_size = os.path.getsize(test_image_path)
            print(f"✅ Test image created successfully!")
            print(f"   📁 Path: {test_image_path}")
            print(f"   📏 Size: {file_size} bytes")
            return True
        else:
            print("❌ Test image was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error testing PIL: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_moviepy_basic():
    """Test basic MoviePy functionality"""
    print("\n🎥 TESTING MOVIEPY BASIC FUNCTIONALITY")
    print("=" * 45)
    
    try:
        from moviepy.editor import ColorClip
        
        print("✅ MoviePy ColorClip imported successfully")
        
        # Create simple video
        clip = ColorClip(size=(640, 480), color=(255, 0, 0), duration=2)
        
        output_path = "temp/test_basic_video.mp4"
        os.makedirs("temp", exist_ok=True)
        
        print("🎬 Creating basic red video...")
        clip.write_videofile(output_path, fps=24, verbose=False, logger=None)
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Basic video created successfully!")
            print(f"   📁 Path: {output_path}")
            print(f"   📏 Size: {file_size} bytes")
            return True
        else:
            print("❌ Basic video was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error testing MoviePy: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 VIDEO CREATION TEST SUITE")
    print("=" * 60)
    
    # Test PIL functionality
    pil_success = test_pil_functionality()
    
    # Test basic MoviePy
    moviepy_success = test_moviepy_basic()
    
    # Test video creation tool
    video_success = test_video_creation()
    
    print("\n📋 SUMMARY")
    print("=" * 30)
    print(f"PIL Test: {'✅ PASSED' if pil_success else '❌ FAILED'}")
    print(f"MoviePy Test: {'✅ PASSED' if moviepy_success else '❌ FAILED'}")
    print(f"Video Creation Test: {'✅ PASSED' if video_success else '❌ FAILED'}")
    
    if pil_success and moviepy_success and video_success:
        print("\n🎉 All tests passed! Video creation is working without ImageMagick.")
        print("💡 The system now uses PIL for text rendering instead of ImageMagick.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        
        if not pil_success:
            print("   • PIL issue: Check Pillow installation")
        if not moviepy_success:
            print("   • MoviePy issue: Check MoviePy installation")
        if not video_success:
            print("   • Video creation issue: Check tool implementation")
