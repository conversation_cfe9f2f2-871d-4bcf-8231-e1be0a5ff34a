import os
import requests

class ElevenLabsTool:
    def run(self, text: str):
        voice_id = "EXAVITQu4vr4xnSDxMaL"  # Grandpa Spuds <PERSON><PERSON> or whichever is configured
        api_key = os.getenv("ELEVENLABS_API_KEY")
        output_path = "output_voice.mp3"

        headers = {
            "xi-api-key": api_key,
            "Content-Type": "application/json"
        }
        data = {
            "text": text,
            "voice_settings": {"stability": 0.7, "similarity_boost": 0.75}
        }

        response = requests.post(
            f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}",
            headers=headers,
            json=data
        )

        if response.ok:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return output_path
        else:
            raise Exception(f"Voice generation failed: {response.text}")
