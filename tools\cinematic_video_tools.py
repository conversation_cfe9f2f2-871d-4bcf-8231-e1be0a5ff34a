#!/usr/bin/env python3
"""
Cinematic Video Production Tools for <PERSON> Quotes Instagram Bot
Advanced video editing with professional effects and transitions
"""

from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import os
import time
import json
import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

# Handle PIL version compatibility
try:
    # For newer Pillow versions
    from PIL.Image import Resampling
    ANTIALIAS = Resampling.LANCZOS
except ImportError:
    # For older Pillow versions
    ANTIALIAS = Image.ANTIALIAS

# Import video processing libraries
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("OpenCV not available - some advanced video effects will be limited")

try:
    from moviepy.editor import *
    from moviepy.video.fx import resize, fadein, fadeout
    from moviepy.audio.fx import volumex
    try:
        from moviepy.video.fx.colorx import colorx
    except ImportError:
        try:
            # Alternative import path
            from moviepy.video.fx import colorx
        except ImportError:
            # Fallback for older MoviePy versions or when not available
            colorx = None
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    colorx = None
    print("MoviePy not available - video creation will be limited")


class CinematicVideoInput(BaseModel):
    quote_text: str = Field(description="Quote text to display in video")
    background_image: str = Field(description="Path to background image")
    voiceover_audio: str = Field(description="Path to voiceover audio file")
    background_music: str = Field(description="Path to background music file")
    video_length: int = Field(default=30, description="Video length in seconds (15-60)")
    style: str = Field(default="cinematic", description="Video style (cinematic, minimal, dynamic)")
    transition_duration: float = Field(default=1.0, description="Transition duration in seconds")

class CinematicVideoTool(BaseTool):
    name: str = "Cinematic Video Producer"
    description: str = "Creates professional cinematic quote videos with advanced effects, transitions, and audio mixing"
    args_schema: Type[BaseModel] = CinematicVideoInput

    def _run(self, quote_text: str, background_image: str, voiceover_audio: str,
             background_music: str, video_length: int = 30, style: str = "cinematic",
             transition_duration: float = 1.0) -> str:
        try:
            if not MOVIEPY_AVAILABLE:
                return self._create_fallback_video(quote_text, video_length)

            # Ensure output directory exists
            os.makedirs("temp", exist_ok=True)

            # Generate output filename
            timestamp = int(time.time())
            output_filename = f"temp/cinematic_video_{style}_{timestamp}.mp4"

            # Create cinematic video with all components
            video_clip = self._create_cinematic_composition(
                quote_text, background_image, voiceover_audio,
                background_music, video_length, style, transition_duration
            )

            if video_clip:
                # Render final video
                video_clip.write_videofile(
                    output_filename,
                    fps=30,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp/temp-audio.m4a',
                    remove_temp=True,
                    verbose=False,
                    logger=None
                )

                # Get file size for verification
                if os.path.exists(output_filename):
                    file_size = os.path.getsize(output_filename)
                    return f"Cinematic video created: {output_filename} | Size: {file_size} bytes | Duration: {video_length}s"
                else:
                    return "Video rendering completed but file not found"
            else:
                return "Failed to create video composition"

        except Exception as e:
            return f"Error creating cinematic video: {str(e)}"

    def _create_cinematic_composition(self, quote_text: str, background_image: str,
                                    voiceover_audio: str, background_music: str,
                                    video_length: int, style: str, transition_duration: float):
        """Create the complete cinematic video composition"""
        try:
            clips = []

            # 1. Create background video with Ken Burns effect
            background_clip = self._create_background_with_ken_burns(
                background_image, video_length, style
            )
            if background_clip:
                clips.append(background_clip)

            # 2. Create animated text overlay
            text_clip = self._create_animated_text(
                quote_text, video_length, style, transition_duration
            )
            if text_clip:
                clips.append(text_clip)

            # 3. Add cinematic effects overlay
            effects_clip = self._create_cinematic_effects(video_length, style)
            if effects_clip:
                clips.append(effects_clip)

            # 4. Composite all visual elements
            if clips:
                video = CompositeVideoClip(clips, size=(1080, 1920))

                # 5. Add audio composition
                final_video = self._add_audio_composition(
                    video, voiceover_audio, background_music, video_length
                )

                # 6. Apply final color grading and effects
                final_video = self._apply_color_grading(final_video, style)

                return final_video

            return None

        except Exception as e:
            print(f"Error creating composition: {e}")
            return None

    def _create_background_with_ken_burns(self, image_path: str, duration: int, style: str):
        """Create background video with Ken Burns effect (slow zoom/pan)"""
        try:
            if not os.path.exists(image_path):
                # Create fallback colored background
                return ColorClip(size=(1080, 1920), color=(139, 69, 19), duration=duration)

            # Load and prepare image
            img_clip = ImageClip(image_path, duration=duration)

            # Resize image to be larger than frame for Ken Burns effect
            img_clip = img_clip.resize(1.2)  # 20% larger for zoom room

            # Apply Ken Burns effect based on style
            if style == "cinematic":
                # Slow zoom in with slight pan
                img_clip = img_clip.resize(lambda t: 1.2 + 0.1 * t / duration)
                img_clip = img_clip.set_position(lambda t: ('center', 'center'))
            elif style == "dynamic":
                # More dramatic zoom and pan
                img_clip = img_clip.resize(lambda t: 1.2 + 0.2 * t / duration)
                img_clip = img_clip.set_position(lambda t: (50 - 20 * t / duration, 'center'))
            else:  # minimal
                # Subtle zoom
                img_clip = img_clip.resize(lambda t: 1.2 + 0.05 * t / duration)

            # Add fade in/out
            img_clip = img_clip.fadein(1.0).fadeout(1.0)

            return img_clip

        except Exception as e:
            print(f"Error creating Ken Burns effect: {e}")
            return ColorClip(size=(1080, 1920), color=(139, 69, 19), duration=duration)

    def _create_animated_text(self, quote_text: str, duration: int, style: str, transition_duration: float):
        """Create animated text overlay with professional typography"""
        try:
            # Create text image with PIL for better control
            text_image_path = self._create_professional_text_image(quote_text, style)

            if not text_image_path or not os.path.exists(text_image_path):
                return None

            # Create text clip
            text_clip = ImageClip(text_image_path, duration=duration)
            text_clip = text_clip.set_position('center')

            # Apply text animation based on style
            if style == "cinematic":
                # Fade in with slight scale
                text_clip = text_clip.fadein(transition_duration).fadeout(transition_duration)
                text_clip = text_clip.resize(lambda t: 0.8 + 0.2 * min(t / transition_duration, 1))
            elif style == "dynamic":
                # Slide in from bottom
                text_clip = text_clip.set_position(lambda t: ('center', 1920 - 1920 * min(t / transition_duration, 1)))
                text_clip = text_clip.fadein(0.5).fadeout(1.0)
            else:  # minimal
                # Simple fade
                text_clip = text_clip.fadein(1.0).fadeout(1.0)

            # Clean up temporary text image
            if text_image_path and os.path.exists(text_image_path):
                try:
                    os.remove(text_image_path)
                except:
                    pass

            return text_clip

        except Exception as e:
            print(f"Error creating animated text: {e}")
            return None

    def _create_professional_text_image(self, quote_text: str, style: str) -> str:
        """Create professional text image with advanced typography"""
        try:
            # Image dimensions
            width, height = 1080, 1920

            # Create transparent image for text
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Style-based font settings
            font_settings = {
                "cinematic": {"size": 65, "color": (255, 255, 255, 255), "shadow": True},
                "dynamic": {"size": 70, "color": (255, 215, 0, 255), "shadow": True},
                "minimal": {"size": 60, "color": (255, 255, 255, 255), "shadow": False}
            }

            settings = font_settings.get(style, font_settings["cinematic"])

            # Load font
            try:
                font = ImageFont.truetype("arial.ttf", settings["size"])
            except:
                font = ImageFont.load_default()

            # Word wrap text
            words = quote_text.split()
            lines = []
            current_line = []

            for word in words:
                test_line = ' '.join(current_line + [word])
                bbox = draw.textbbox((0, 0), test_line, font=font)
                text_width = bbox[2] - bbox[0]

                if text_width <= width - 120:  # 60px margin on each side
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)

            if current_line:
                lines.append(' '.join(current_line))

            # Calculate text positioning
            line_height = settings["size"] + 20
            total_text_height = len(lines) * line_height
            start_y = (height - total_text_height) // 2

            # Draw text with professional styling
            for i, line in enumerate(lines):
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                x = (width - text_width) // 2
                y = start_y + i * line_height

                # Add shadow if specified
                if settings["shadow"]:
                    shadow_offset = 3
                    draw.text((x + shadow_offset, y + shadow_offset), line,
                             font=font, fill=(0, 0, 0, 180))

                # Draw main text
                draw.text((x, y), line, font=font, fill=settings["color"])

            # Save text image
            timestamp = int(time.time())
            text_image_path = f"temp/text_overlay_{style}_{timestamp}.png"
            img.save(text_image_path)

            return text_image_path

        except Exception as e:
            print(f"Error creating text image: {e}")
            return None

    def _create_cinematic_effects(self, duration: int, style: str):
        """Create cinematic effects overlay (vignette, film grain, etc.)"""
        try:
            if style == "minimal":
                return None  # No effects for minimal style

            # Create effects image
            effects_img = Image.new('RGBA', (1080, 1920), (0, 0, 0, 0))
            draw = ImageDraw.Draw(effects_img)

            if style == "cinematic":
                # Add subtle vignette
                self._add_vignette(draw, 1080, 1920, intensity=0.3)
            elif style == "dynamic":
                # Add more dramatic effects
                self._add_vignette(draw, 1080, 1920, intensity=0.5)
                self._add_film_grain(effects_img)

            # Save effects image
            timestamp = int(time.time())
            effects_path = f"temp/effects_{style}_{timestamp}.png"
            effects_img.save(effects_path)

            # Create effects clip
            effects_clip = ImageClip(effects_path, duration=duration)
            effects_clip = effects_clip.set_position('center')

            # Clean up
            if os.path.exists(effects_path):
                try:
                    os.remove(effects_path)
                except:
                    pass

            return effects_clip

        except Exception as e:
            print(f"Error creating effects: {e}")
            return None

    def _add_vignette(self, draw, width: int, height: int, intensity: float = 0.3):
        """Add vignette effect to image"""
        center_x, center_y = width // 2, height // 2
        max_distance = ((width ** 2 + height ** 2) ** 0.5) / 2

        # Create radial gradient for vignette
        for y in range(0, height, 20):  # Sample every 20 pixels for performance
            for x in range(0, width, 20):
                distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                alpha = int(255 * intensity * (distance / max_distance))
                alpha = min(alpha, 255)

                # Draw small rectangle with vignette color
                draw.rectangle([x, y, x + 20, y + 20], fill=(0, 0, 0, alpha))

    def _add_film_grain(self, img: Image.Image):
        """Add subtle film grain effect"""
        try:
            # Convert to numpy array for grain processing
            img_array = np.array(img)

            # Generate noise
            noise = np.random.randint(-10, 10, img_array.shape[:2])

            # Apply noise to RGB channels
            for i in range(3):  # RGB channels
                channel = img_array[:, :, i].astype(np.int16)
                channel += noise
                channel = np.clip(channel, 0, 255)
                img_array[:, :, i] = channel.astype(np.uint8)

            # Convert back to PIL Image
            return Image.fromarray(img_array)

        except Exception as e:
            print(f"Error adding film grain: {e}")
            return img

    def _add_audio_composition(self, video_clip, voiceover_path: str, music_path: str, duration: int):
        """Add professional audio composition with ducking and mixing"""
        try:
            audio_clips = []

            # Add voiceover if available
            if voiceover_path and os.path.exists(voiceover_path):
                try:
                    voiceover = AudioFileClip(voiceover_path)
                    # Ensure voiceover fits video duration
                    if voiceover.duration > duration:
                        voiceover = voiceover.subclip(0, duration)
                    elif voiceover.duration < duration:
                        # Pad with silence if needed
                        silence = AudioClip(lambda t: 0, duration=duration - voiceover.duration)
                        voiceover = concatenate_audioclips([voiceover, silence])

                    audio_clips.append(voiceover)
                except Exception as e:
                    print(f"Error loading voiceover: {e}")

            # Add background music if available
            if music_path and os.path.exists(music_path):
                try:
                    music = AudioFileClip(music_path)

                    # Loop music if shorter than video
                    if music.duration < duration:
                        loops_needed = int(duration / music.duration) + 1
                        music = concatenate_audioclips([music] * loops_needed)

                    # Trim to video duration
                    music = music.subclip(0, duration)

                    # Apply audio ducking (lower music when voice is present)
                    if len(audio_clips) > 0:  # If voiceover exists
                        music = music.volumex(0.3)  # Lower background music volume
                    else:
                        music = music.volumex(0.6)  # Normal music volume

                    # Add fade in/out
                    music = music.fadein(2.0).fadeout(2.0)

                    audio_clips.append(music)
                except Exception as e:
                    print(f"Error loading background music: {e}")

            # Composite audio
            if audio_clips:
                final_audio = CompositeAudioClip(audio_clips)
                video_clip = video_clip.set_audio(final_audio)

            return video_clip

        except Exception as e:
            print(f"Error composing audio: {e}")
            return video_clip

    def _apply_color_grading(self, video_clip, style: str):
        """Apply cinematic color grading and final effects"""
        try:
            if colorx is not None:
                if style == "cinematic":
                    # Warm cinematic look
                    video_clip = video_clip.fx(colorx, 1.1)  # Slight color boost
                elif style == "dynamic":
                    # High contrast, saturated look
                    video_clip = video_clip.fx(colorx, 1.3)  # Higher saturation
            else:
                print("Color grading not available - colorx not found in MoviePy")
            # Minimal style gets no color grading

            return video_clip

        except Exception as e:
            print(f"Error applying color grading: {e}")
            return video_clip

    def _create_fallback_video(self, quote_text: str, duration: int) -> str:
        """Create fallback video when MoviePy is not available"""
        try:
            # Create simple text-based video info
            timestamp = int(time.time())
            fallback_filename = f"temp/video_fallback_{timestamp}.txt"

            with open(fallback_filename, 'w', encoding='utf-8') as f:
                f.write(f"Cinematic Video Project\n")
                f.write(f"========================\n\n")
                f.write(f"Quote: {quote_text}\n")
                f.write(f"Duration: {duration} seconds\n")
                f.write(f"Format: Instagram Story (1080x1920)\n")
                f.write(f"Style: Professional cinematic production\n\n")
                f.write("Features to include:\n")
                f.write("- Ken Burns effect on background image\n")
                f.write("- Animated text with professional typography\n")
                f.write("- Cinematic color grading\n")
                f.write("- Audio ducking and mixing\n")
                f.write("- Fade transitions\n")
                f.write("- Vignette and film grain effects\n\n")
                f.write("Note: MoviePy not available - install with: pip install moviepy opencv-python\n")

            return f"Fallback video project: {fallback_filename}"

        except Exception as e:
            return f"Error creating fallback: {str(e)}"
