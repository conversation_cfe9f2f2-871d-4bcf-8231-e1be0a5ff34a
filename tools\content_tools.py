from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import random
import os
import json

class TrendResearchInput(BaseModel):
    keywords: list = Field(description="List of keywords to research trends for")

class TrendResearchTool(BaseTool):
    name: str = "Trend Research Tool"
    description: str = "Researches trending topics using RapidAPI Google Trends"
    args_schema: Type[BaseModel] = TrendResearchInput

    def _run(self, keywords: list) -> str:
        try:
            # Get RapidAPI key from environment
            rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
            if not rapidapi_key:
                return "Error: RapidAPI key not configured"

            # Use Google Trends API via RapidAPI
            url = "https://google-trends9.p.rapidapi.com/trending"

            headers = {
                "X-RapidAPI-Key": rapidapi_key,
                "X-RapidAPI-Host": "google-trends9.p.rapidapi.com"
            }

            # Get trending topics for the first keyword
            querystring = {"geo": "US", "hl": "en"}

            response = requests.get(url, headers=headers, params=querystring)

            if response.status_code == 200:
                data = response.json()
                trending_topics = []

                # Extract trending topics from response
                if isinstance(data, dict) and 'trending_searches' in data:
                    for item in data['trending_searches'][:5]:
                        if 'title' in item:
                            trending_topics.append(item['title'])
                elif isinstance(data, list):
                    for item in data[:5]:
                        if isinstance(item, dict) and 'title' in item:
                            trending_topics.append(item['title'])
                        elif isinstance(item, str):
                            trending_topics.append(item)

                if trending_topics:
                    return f"Trending topics: {', '.join(trending_topics)}"
                else:
                    # Fallback to keyword-related topics
                    return f"Related topics for {', '.join(keywords)}: mindfulness, meditation, inner peace, spiritual growth, wisdom"
            else:
                # Fallback if API fails
                return f"Trending topics related to {', '.join(keywords)}: mindfulness, meditation, peace, wisdom, spiritual growth"

        except Exception as e:
            # Fallback with predefined spiritual topics
            return f"Trending spiritual topics: mindfulness, meditation, inner peace, wisdom, gratitude"

class QuoteGeneratorInput(BaseModel):
    topic: str = Field(description="Topic to generate Buddha quote about")

class QuoteGeneratorTool(BaseTool):
    name: str = "Quote Generator Tool"
    description: str = "Generates inspirational quotes using RapidAPI"
    args_schema: Type[BaseModel] = QuoteGeneratorInput

    def _run(self, topic: str) -> str:
        try:
            # First try free quotable.io API
            try:
                # Get random inspirational quote from quotable.io
                url = "https://api.quotable.io/random"
                params = {
                    "tags": "inspirational|motivational|wisdom",
                    "maxLength": 150  # Instagram character limit
                }

                response = requests.get(url, params=params, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if 'content' in data and 'author' in data:
                        quote = data['content']
                        author = data['author']
                        if len(quote) <= 120:  # Leave room for attribution
                            return f"{quote} - {author}"
                        else:
                            return quote
            except:
                pass

            # Try ZenQuotes API as backup
            try:
                url = "https://zenquotes.io/api/random"
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if isinstance(data, list) and len(data) > 0:
                        quote_data = data[0]
                        if 'q' in quote_data and 'a' in quote_data:
                            quote = quote_data['q']
                            author = quote_data['a']
                            if len(quote) <= 120:
                                return f"{quote} - {author}"
                            else:
                                return quote
            except:
                pass

            # Try RapidAPI as backup if available
            rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
            if rapidapi_key:
                try:
                    url = "https://quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com/quote"
                    headers = {
                        "X-RapidAPI-Key": rapidapi_key,
                        "X-RapidAPI-Host": "quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com"
                    }
                    querystring = {"token": "ihj1QfArLx", "cat": "motivational"}
                    response = requests.get(url, headers=headers, params=querystring, timeout=5)

                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, dict) and 'text' in data:
                            quote = data['text']
                            if len(quote) <= 150:
                                return quote
                except:
                    pass

            # Fallback to predefined Buddha-style quotes
            quote_templates = [
                "Peace comes from within. Do not seek it without.",
                "The mind is everything. What you think you become.",
                "In the end, only three things matter: how much you loved, how gently you lived, and how gracefully you let go.",
                "Happiness does not depend on what you have or who you are. It solely relies on what you think.",
                "Better than a thousand hollow words, is one word that brings peace.",
                "Do not dwell in the past, do not dream of the future, concentrate the mind on the present moment.",
                "Three things cannot be long hidden: the sun, the moon, and the truth.",
                "The only real failure in life is not to be true to the best one knows.",
                "Hatred does not cease by hatred, but only by love; this is the eternal rule.",
                "Health is the greatest gift, contentment the greatest wealth, faithfulness the best relationship."
            ]

            # Filter quotes by topic relevance if possible
            topic_lower = topic.lower()
            relevant_quotes = []

            for quote in quote_templates:
                if any(word in quote.lower() for word in [topic_lower, 'peace', 'mind', 'love', 'happiness']):
                    relevant_quotes.append(quote)

            if relevant_quotes:
                return random.choice(relevant_quotes)
            else:
                return random.choice(quote_templates)

        except Exception as e:
            # Ultimate fallback
            return "Peace comes from within. Do not seek it without."


class SocialMediaTrendsInput(BaseModel):
    platform: str = Field(default="instagram", description="Social media platform to get trends for")

class SocialMediaTrendsTool(BaseTool):
    name: str = "Social Media Trends Tool"
    description: str = "Gets trending hashtags and topics from social media platforms using RapidAPI"
    args_schema: Type[BaseModel] = SocialMediaTrendsInput

    def _run(self, platform: str = "instagram") -> str:
        try:
            # Get RapidAPI key from environment
            rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')

            if rapidapi_key:
                # Try to get Twitter/X trends which are often relevant for Instagram too
                url = "https://twitter-trends8.p.rapidapi.com/trends24"

                headers = {
                    "X-RapidAPI-Key": rapidapi_key,
                    "X-RapidAPI-Host": "twitter-trends8.p.rapidapi.com"
                }

                try:
                    response = requests.get(url, headers=headers, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        trending_hashtags = []

                        # Extract hashtags from response
                        if isinstance(data, dict):
                            if 'trends' in data:
                                for trend in data['trends'][:10]:
                                    if isinstance(trend, dict) and 'name' in trend:
                                        hashtag = trend['name']
                                        if hashtag.startswith('#'):
                                            trending_hashtags.append(hashtag)
                                        else:
                                            trending_hashtags.append(f"#{hashtag}")
                            elif 'data' in data:
                                for item in data['data'][:10]:
                                    if isinstance(item, dict) and 'hashtag' in item:
                                        trending_hashtags.append(item['hashtag'])

                        if trending_hashtags:
                            # Filter for spiritual/motivational relevant hashtags
                            spiritual_keywords = ['peace', 'mind', 'soul', 'spirit', 'wisdom', 'love', 'life', 'motivation', 'inspiration', 'meditation', 'mindfulness']
                            relevant_hashtags = []

                            for hashtag in trending_hashtags:
                                hashtag_lower = hashtag.lower()
                                if any(keyword in hashtag_lower for keyword in spiritual_keywords):
                                    relevant_hashtags.append(hashtag)

                            if relevant_hashtags:
                                return f"Trending spiritual hashtags: {', '.join(relevant_hashtags[:5])}"
                            else:
                                return f"General trending hashtags: {', '.join(trending_hashtags[:5])}"
                except:
                    pass

            # Fallback to curated spiritual hashtags
            spiritual_hashtags = [
                "#mindfulness", "#meditation", "#innerpeace", "#wisdom", "#spirituality",
                "#motivation", "#inspiration", "#peace", "#love", "#gratitude",
                "#consciousness", "#enlightenment", "#buddha", "#zen", "#mindful"
            ]

            return f"Recommended spiritual hashtags: {', '.join(random.sample(spiritual_hashtags, 5))}"

        except Exception as e:
            # Ultimate fallback
            return "Recommended hashtags: #mindfulness #peace #wisdom #meditation #inspiration"