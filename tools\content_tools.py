from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import random
import os
import json

class TrendResearchInput(BaseModel):
    keywords: list = Field(description="List of keywords to research trends for")

class TrendResearchTool(BaseTool):
    name: str = "Trend Research Tool"
    description: str = "Researches trending topics using RapidAPI Google Trends"
    args_schema: Type[BaseModel] = TrendResearchInput

    def _run(self, keywords: list) -> str:
        try:
            trending_topics = []

            # Try NewsAPI for trending topics
            try:
                newsapi_key = os.getenv('NEWSAPI_KEY')
                if newsapi_key and newsapi_key != 'your_newsapi_key_here':
                    url = "https://newsapi.org/v2/top-headlines"
                    params = {
                        "apiKey": newsapi_key,
                        "country": "us",
                        "category": "health",
                        "pageSize": 10
                    }

                    response = requests.get(url, params=params, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if 'articles' in data:
                            for article in data['articles'][:5]:
                                if 'title' in article:
                                    title = article['title']
                                    # Extract keywords related to wellness/spirituality
                                    wellness_keywords = ['health', 'wellness', 'mental', 'mindfulness', 'meditation', 'stress', 'peace', 'happiness']
                                    if any(keyword.lower() in title.lower() for keyword in wellness_keywords):
                                        trending_topics.append(title.split(' - ')[0])  # Remove source
            except:
                pass

            # Try Reddit API for trending topics (no auth needed for some endpoints)
            try:
                url = "https://www.reddit.com/r/getmotivated/hot.json"
                headers = {'User-Agent': 'BuddhaBot/1.0'}
                response = requests.get(url, headers=headers, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if 'data' in data and 'children' in data['data']:
                        for post in data['data']['children'][:3]:
                            if 'data' in post and 'title' in post['data']:
                                title = post['data']['title']
                                if len(title) < 100:  # Keep it concise
                                    trending_topics.append(title)
            except:
                pass

            # Try HackerNews API for tech/productivity trends
            try:
                url = "https://hacker-news.firebaseio.com/v0/topstories.json"
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    story_ids = response.json()[:5]
                    for story_id in story_ids:
                        story_url = f"https://hacker-news.firebaseio.com/v0/item/{story_id}.json"
                        story_response = requests.get(story_url, timeout=5)
                        if story_response.status_code == 200:
                            story_data = story_response.json()
                            if 'title' in story_data:
                                title = story_data['title']
                                # Look for productivity/wellness related topics
                                if any(word in title.lower() for word in ['productivity', 'wellness', 'mental', 'health', 'meditation']):
                                    trending_topics.append(title)
            except:
                pass

            # Filter and return trending topics
            if trending_topics:
                unique_topics = list(set(trending_topics))[:5]
                return f"Trending topics: {', '.join(unique_topics)}"
            else:
                # Fallback to curated spiritual topics based on keywords
                spiritual_topics = {
                    'mindfulness': ['present moment awareness', 'mindful living', 'conscious breathing'],
                    'meditation': ['daily meditation practice', 'inner peace', 'spiritual awakening'],
                    'peace': ['inner harmony', 'peaceful mind', 'tranquility'],
                    'wisdom': ['ancient wisdom', 'life lessons', 'spiritual insights'],
                    'love': ['self-love', 'compassion', 'loving kindness']
                }

                relevant_topics = []
                for keyword in keywords:
                    keyword_lower = keyword.lower()
                    if keyword_lower in spiritual_topics:
                        relevant_topics.extend(spiritual_topics[keyword_lower])

                if relevant_topics:
                    return f"Related spiritual topics: {', '.join(relevant_topics[:5])}"
                else:
                    return "Trending spiritual topics: mindfulness, meditation, inner peace, wisdom, gratitude"

        except Exception as e:
            # Ultimate fallback
            return "Trending spiritual topics: mindfulness, meditation, inner peace, wisdom, gratitude"

class QuoteGeneratorInput(BaseModel):
    topic: str = Field(description="Topic to generate Buddha quote about")

class QuoteGeneratorTool(BaseTool):
    name: str = "Quote Generator Tool"
    description: str = "Generates inspirational quotes using RapidAPI"
    args_schema: Type[BaseModel] = QuoteGeneratorInput

    def _run(self, topic: str) -> str:
        try:
            # First try free quotable.io API
            try:
                # Get random inspirational quote from quotable.io
                url = "https://api.quotable.io/random"
                params = {
                    "tags": "inspirational|motivational|wisdom",
                    "maxLength": 150  # Instagram character limit
                }

                response = requests.get(url, params=params, timeout=10, verify=False)

                if response.status_code == 200:
                    data = response.json()
                    if 'content' in data and 'author' in data:
                        quote = data['content']
                        author = data['author']
                        if len(quote) <= 120:  # Leave room for attribution
                            return f"{quote} - {author}"
                        else:
                            return quote
            except:
                pass

            # Try ZenQuotes API as backup
            try:
                url = "https://zenquotes.io/api/random"
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    if isinstance(data, list) and len(data) > 0:
                        quote_data = data[0]
                        if 'q' in quote_data and 'a' in quote_data:
                            quote = quote_data['q']
                            author = quote_data['a']
                            if len(quote) <= 120:
                                return f"{quote} - {author}"
                            else:
                                return quote
            except:
                pass

            # Try RapidAPI as backup if available
            rapidapi_key = os.getenv('RAPIDAPI_KEY') or os.getenv('GOOGLE_TRENDS_API_KEY')
            if rapidapi_key:
                try:
                    url = "https://quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com/quote"
                    headers = {
                        "X-RapidAPI-Key": rapidapi_key,
                        "X-RapidAPI-Host": "quotes-inspirational-quotes-motivational-quotes.p.rapidapi.com"
                    }
                    querystring = {"token": "ihj1QfArLx", "cat": "motivational"}
                    response = requests.get(url, headers=headers, params=querystring, timeout=5)

                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, dict) and 'text' in data:
                            quote = data['text']
                            if len(quote) <= 150:
                                return quote
                except:
                    pass

            # Fallback to predefined Buddha-style quotes
            quote_templates = [
                "Peace comes from within. Do not seek it without.",
                "The mind is everything. What you think you become.",
                "In the end, only three things matter: how much you loved, how gently you lived, and how gracefully you let go.",
                "Happiness does not depend on what you have or who you are. It solely relies on what you think.",
                "Better than a thousand hollow words, is one word that brings peace.",
                "Do not dwell in the past, do not dream of the future, concentrate the mind on the present moment.",
                "Three things cannot be long hidden: the sun, the moon, and the truth.",
                "The only real failure in life is not to be true to the best one knows.",
                "Hatred does not cease by hatred, but only by love; this is the eternal rule.",
                "Health is the greatest gift, contentment the greatest wealth, faithfulness the best relationship."
            ]

            # Filter quotes by topic relevance if possible
            topic_lower = topic.lower()
            relevant_quotes = []

            for quote in quote_templates:
                if any(word in quote.lower() for word in [topic_lower, 'peace', 'mind', 'love', 'happiness']):
                    relevant_quotes.append(quote)

            if relevant_quotes:
                return random.choice(relevant_quotes)
            else:
                return random.choice(quote_templates)

        except Exception as e:
            # Ultimate fallback
            return "Peace comes from within. Do not seek it without."


class SocialMediaTrendsInput(BaseModel):
    platform: str = Field(default="instagram", description="Social media platform to get trends for")

class SocialMediaTrendsTool(BaseTool):
    name: str = "Social Media Trends Tool"
    description: str = "Gets trending hashtags and topics from social media platforms using RapidAPI"
    args_schema: Type[BaseModel] = SocialMediaTrendsInput

    def _run(self, platform: str = "instagram") -> str:
        try:
            trending_hashtags = []

            # Try to get trending hashtags from Reddit spiritual communities
            try:
                subreddits = ['meditation', 'mindfulness', 'spirituality', 'getmotivated']
                headers = {'User-Agent': 'BuddhaBot/1.0'}

                for subreddit in subreddits[:2]:  # Limit to avoid rate limits
                    url = f"https://www.reddit.com/r/{subreddit}/hot.json?limit=5"
                    response = requests.get(url, headers=headers, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data and 'children' in data['data']:
                            for post in data['data']['children']:
                                if 'data' in post:
                                    title = post['data'].get('title', '')
                                    selftext = post['data'].get('selftext', '')

                                    # Extract hashtag-like words
                                    text = f"{title} {selftext}".lower()
                                    words = text.split()

                                    for word in words:
                                        if len(word) > 3 and any(keyword in word for keyword in ['mind', 'peace', 'love', 'spirit', 'meditat', 'zen']):
                                            hashtag = f"#{word.replace('#', '')}"
                                            if hashtag not in trending_hashtags:
                                                trending_hashtags.append(hashtag)
            except:
                pass

            # Try to analyze current wellness trends from news
            try:
                newsapi_key = os.getenv('NEWSAPI_KEY')
                if newsapi_key and newsapi_key != 'your_newsapi_key_here':
                    url = "https://newsapi.org/v2/everything"
                    params = {
                        "apiKey": newsapi_key,
                        "q": "mindfulness OR meditation OR wellness",
                        "sortBy": "popularity",
                        "pageSize": 5,
                        "language": "en"
                    }

                    response = requests.get(url, params=params, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if 'articles' in data:
                            for article in data['articles']:
                                title = article.get('title', '').lower()
                                description = article.get('description', '').lower()

                                # Extract trending wellness terms
                                text = f"{title} {description}"
                                wellness_terms = ['mindfulness', 'meditation', 'wellness', 'selfcare', 'mentalhealth', 'peace', 'gratitude']

                                for term in wellness_terms:
                                    if term in text:
                                        hashtag = f"#{term}"
                                        if hashtag not in trending_hashtags:
                                            trending_hashtags.append(hashtag)
            except:
                pass

            # Curated spiritual hashtags based on current trends and platform
            platform_hashtags = {
                'instagram': [
                    "#mindfulness", "#meditation", "#innerpeace", "#wisdom", "#spirituality",
                    "#motivation", "#inspiration", "#peace", "#love", "#gratitude",
                    "#consciousness", "#enlightenment", "#buddha", "#zen", "#mindful",
                    "#selfcare", "#mentalhealth", "#wellness", "#positivity", "#healing"
                ],
                'twitter': [
                    "#mindfulness", "#meditation", "#wisdom", "#peace", "#motivation",
                    "#inspiration", "#spirituality", "#zen", "#gratitude", "#mindful"
                ],
                'tiktok': [
                    "#mindfulness", "#meditation", "#spiritualtiktok", "#wisdom", "#peace",
                    "#motivation", "#inspiration", "#zen", "#mindful", "#healing"
                ]
            }

            # Get platform-specific hashtags
            curated_hashtags = platform_hashtags.get(platform.lower(), platform_hashtags['instagram'])

            # Combine trending and curated hashtags
            if trending_hashtags:
                # Mix trending with curated
                all_hashtags = trending_hashtags + curated_hashtags
                unique_hashtags = list(dict.fromkeys(all_hashtags))  # Remove duplicates while preserving order
                return f"Trending spiritual hashtags: {', '.join(unique_hashtags[:8])}"
            else:
                # Use curated hashtags with some randomization
                selected_hashtags = random.sample(curated_hashtags, min(8, len(curated_hashtags)))
                return f"Recommended spiritual hashtags: {', '.join(selected_hashtags)}"

        except Exception as e:
            # Ultimate fallback
            return "Recommended hashtags: #mindfulness #peace #wisdom #meditation #inspiration #love #gratitude #zen"