from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
from pytrends.request import TrendReq
import random

class TrendResearchInput(BaseModel):
    keywords: list = Field(description="List of keywords to research trends for")

class TrendResearchTool(BaseTool):
    name: str = "Trend Research Tool"
    description: str = "Researches trending topics using Google Trends"
    args_schema: Type[BaseModel] = TrendResearchInput

    def _run(self, keywords: list) -> str:
        try:
            pytrends = TrendReq(hl='en-US', tz=360)
            pytrends.build_payload(keywords, cat=0, timeframe='today 1-m')

            related_queries = pytrends.related_queries()
            trending_topics = []

            for keyword in keywords:
                if keyword in related_queries and related_queries[keyword]['top'] is not None:
                    topics = related_queries[keyword]['top']['query'].head(3).tolist()
                    trending_topics.extend(topics)

            return f"Trending topics: {', '.join(trending_topics[:5])}"
        except Exception as e:
            return f"Error researching trends: {str(e)}"

class QuoteGeneratorInput(BaseModel):
    topic: str = Field(description="Topic to generate Buddha quote about")

class QuoteGeneratorTool(BaseTool):
    name: str = "Quote Generator Tool"
    description: str = "Generates Buddha-style motivational quotes"
    args_schema: Type[BaseModel] = QuoteGeneratorInput

    def _run(self, topic: str) -> str:
        # Predefined Buddha-style quotes for reliability
        quote_templates = [
            f"Peace comes from within regarding {topic}. Do not seek it without.",
            f"The mind is everything with {topic}. What you think you become.",
            f"In the end regarding {topic}, only three things matter: how much you loved, how gently you lived, and how gracefully you let go.",
            f"Happiness does not depend on what you have or who you are regarding {topic}. It solely relies on what you think.",
            f"Better than a thousand hollow words about {topic}, is one word that brings peace."
        ]
        return random.choice(quote_templates)