from crewai_tools import BaseTool
from instagrapi import Client
from typing import Type
from pydantic import BaseModel, Field
import os

class InstagramPostInput(BaseModel):
    video_path: str = Field(description="Path to the video file")
    caption: str = Field(description="Caption for the Instagram post")

class InstagramPostTool(BaseTool):
    name: str = "Instagram Post Tool"
    description: str = "Posts video content to Instagram with caption and hashtags"
    args_schema: Type[BaseModel] = InstagramPostInput

    def _run(self, video_path: str, caption: str) -> str:
        try:
            cl = Client()
            cl.login(os.getenv("INSTA_USERNAME"), os.getenv("INSTA_PASSWORD"))
            
            hashtags = "\n\n#Buddha #Mindfulness #Peace #Wisdom #Motivation #Quotes"
            full_caption = f"{caption}{hashtags}"
            
            cl.clip_upload(
                video_path,
                caption=full_caption,
                extra_data={
                    "share_to_facebook": True,
                    "like_and_view_counts_disabled": False,
                }
            )
            return f"Successfully posted video to Instagram: {video_path}"
        except Exception as e:
            return f"Failed to post to Instagram: {str(e)}"