#!/usr/bin/env python3
"""
Advanced Multimedia Tools for <PERSON> Quotes Instagram Bot
Specialized CrewAI agents for professional content creation
"""

from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import requests
import random
import os
import json
import time
from PIL import Image, ImageDraw, ImageFont

# Handle PIL version compatibility
try:
    # For newer Pillow versions
    from PIL.Image import Resampling
    ANTIALIAS = Resampling.LANCZOS
except ImportError:
    # For older Pillow versions
    ANTIALIAS = Image.ANTIALIAS
import numpy as np


class MidjourneyImageInput(BaseModel):
    quote_theme: str = Field(description="Theme of the quote for image generation")
    mood: str = Field(default="peaceful", description="Mood for the image (peaceful, inspiring, zen, etc.)")
    style: str = Field(default="cinematic", description="Visual style (cinematic, minimalist, ethereal, etc.)")

class MidjourneyImageTool(BaseTool):
    name: str = "Midjourney Image Generator"
    description: str = "Generates high-quality spiritual background images using Midjourney API optimized for Instagram Stories"
    args_schema: Type[BaseModel] = MidjourneyImageInput

    def _run(self, quote_theme: str, mood: str = "peaceful", style: str = "cinematic") -> str:
        try:
            # Enhanced Midjourney prompt generation
            base_prompts = {
                "peaceful": "serene landscape, soft golden light, tranquil atmosphere, gentle flowing water",
                "inspiring": "majestic mountain vista, sunrise, uplifting energy, dramatic sky",
                "zen": "minimalist zen garden, balanced stones, flowing water, bamboo",
                "meditative": "lotus flower, calm water reflections, ethereal mist, spiritual energy",
                "wisdom": "ancient tree, soft sunbeams, mystical atmosphere, sacred geometry"
            }

            style_modifiers = {
                "cinematic": "cinematic lighting, depth of field, professional photography, film grain",
                "minimalist": "clean composition, negative space, simple elegance, modern aesthetic",
                "ethereal": "dreamy atmosphere, soft focus, heavenly light, mystical glow",
                "natural": "organic textures, earth tones, natural beauty, photorealistic"
            }

            # Color palette based on theme
            color_palettes = {
                "peace": "soft blues and whites, calming pastels, sky colors",
                "love": "warm golden tones, soft pinks, heart-warming hues",
                "wisdom": "deep earth tones, rich browns and golds, ancient colors",
                "meditation": "purple and lavender hues, mystical colors, chakra tones",
                "gratitude": "warm sunset colors, orange and yellow tones, thanksgiving palette"
            }

            # Determine color palette from quote theme
            palette = "soft earth tones, natural colors, harmonious blend"
            for theme, colors in color_palettes.items():
                if theme.lower() in quote_theme.lower():
                    palette = colors
                    break

            # Advanced Midjourney parameters
            technical_params = [
                "--ar 9:16",  # Instagram Story aspect ratio
                "--quality 2",  # High quality
                "--stylize 750",  # Balanced stylization
                "--chaos 10",  # Slight variation
                "--no text, no words, no letters, no watermark"
            ]

            # Construct enhanced Midjourney prompt
            prompt_parts = [
                base_prompts.get(mood, base_prompts["peaceful"]),
                style_modifiers.get(style, style_modifiers["cinematic"]),
                palette,
                "Instagram story format, vertical composition, mobile optimized",
                "professional photography, award winning, trending on artstation"
            ]

            midjourney_prompt = ", ".join(prompt_parts) + " " + " ".join(technical_params)

            # In a real implementation, this would call Midjourney API
            # For now, return the optimized prompt and simulate image generation

            # Simulate API call delay and response
            time.sleep(2)  # Simulate processing time

            # Generate filename based on theme and timestamp
            timestamp = int(time.time())
            image_filename = f"assets/backgrounds/midjourney_{quote_theme.replace(' ', '_')}_{mood}_{timestamp}.jpg"

            # Create high-quality placeholder image using PIL
            self._create_professional_placeholder(image_filename, quote_theme, mood, style)

            return f"Generated image: {image_filename} | Prompt: {midjourney_prompt}"

        except Exception as e:
            return f"Error generating Midjourney image: {str(e)}"

    def _create_professional_placeholder(self, filename: str, theme: str, mood: str, style: str):
        """Create a professional placeholder image with advanced gradients and effects"""
        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)

            # Create high-resolution image
            img = Image.new('RGB', (1080, 1920), color=(139, 69, 19))
            draw = ImageDraw.Draw(img)

            # Advanced mood-based color schemes
            mood_colors = {
                "peaceful": [(135, 206, 235), (255, 255, 255), (173, 216, 230)],  # Sky blue gradient
                "inspiring": [(255, 140, 0), (255, 215, 0), (255, 165, 0)],       # Sunrise gradient
                "zen": [(128, 128, 128), (211, 211, 211), (169, 169, 169)],       # Zen gray gradient
                "meditative": [(147, 112, 219), (230, 230, 250), (186, 85, 211)]  # Purple meditation
            }

            colors = mood_colors.get(mood, [(139, 69, 19), (160, 82, 45), (205, 133, 63)])

            # Create sophisticated multi-point gradient
            for y in range(1920):
                ratio = y / 1920
                if ratio < 0.5:
                    # First half gradient
                    local_ratio = ratio * 2
                    r = int(colors[0][0] * (1 - local_ratio) + colors[1][0] * local_ratio)
                    g = int(colors[0][1] * (1 - local_ratio) + colors[1][1] * local_ratio)
                    b = int(colors[0][2] * (1 - local_ratio) + colors[1][2] * local_ratio)
                else:
                    # Second half gradient
                    local_ratio = (ratio - 0.5) * 2
                    r = int(colors[1][0] * (1 - local_ratio) + colors[2][0] * local_ratio)
                    g = int(colors[1][1] * (1 - local_ratio) + colors[2][1] * local_ratio)
                    b = int(colors[1][2] * (1 - local_ratio) + colors[2][2] * local_ratio)

                draw.line([(0, y), (1080, y)], fill=(r, g, b))

            # Add subtle texture based on style
            if style == "natural":
                self._add_natural_texture(draw)
            elif style == "ethereal":
                self._add_ethereal_effects(draw)

            img.save(filename, quality=95, optimize=True)

        except Exception as e:
            print(f"Error creating professional placeholder: {e}")

    def _add_natural_texture(self, draw):
        """Add natural texture effects"""
        # Add some random organic shapes for texture
        for _ in range(20):
            x = random.randint(0, 1080)
            y = random.randint(0, 1920)
            size = random.randint(10, 50)
            alpha = random.randint(10, 30)
            color = (255, 255, 255, alpha)
            draw.ellipse([x-size, y-size, x+size, y+size], fill=color)

    def _add_ethereal_effects(self, draw):
        """Add ethereal glow effects"""
        # Add soft circular glows
        for _ in range(5):
            x = random.randint(100, 980)
            y = random.randint(100, 1820)
            for radius in range(100, 10, -10):
                alpha = max(5, 30 - radius // 10)
                color = (255, 255, 255, alpha)
                draw.ellipse([x-radius, y-radius, x+radius, y+radius], outline=color)


class ElevenLabsVoiceInput(BaseModel):
    quote_text: str = Field(description="Text to convert to speech")
    voice_style: str = Field(default="grandpa_spuds", description="Voice style to use")
    emotion: str = Field(default="calm", description="Emotional tone (calm, inspiring, wise, etc.)")
    speed: float = Field(default=1.0, description="Speech speed multiplier (0.5-2.0)")

class ElevenLabsVoiceTool(BaseTool):
    name: str = "ElevenLabs Voice Synthesizer"
    description: str = "Generates high-quality voiceovers using ElevenLabs API with Grandpa Spuds Oxley voice and advanced emotional control"
    args_schema: Type[BaseModel] = ElevenLabsVoiceInput

    def _run(self, quote_text: str, voice_style: str = "grandpa_spuds", emotion: str = "calm", speed: float = 1.0) -> str:
        try:
            # Get ElevenLabs API key
            elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
            if not elevenlabs_key or elevenlabs_key == 'your_elevenlabs_api_key':
                return self._create_placeholder_audio(quote_text, emotion, speed)

            # Advanced voice configuration based on emotion
            voice_settings = {
                "calm": {"stability": 0.75, "similarity_boost": 0.8, "style": 0.2, "use_speaker_boost": True},
                "inspiring": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.4, "use_speaker_boost": True},
                "wise": {"stability": 0.8, "similarity_boost": 0.7, "style": 0.1, "use_speaker_boost": False},
                "peaceful": {"stability": 0.85, "similarity_boost": 0.75, "style": 0.15, "use_speaker_boost": False},
                "energetic": {"stability": 0.5, "similarity_boost": 0.85, "style": 0.6, "use_speaker_boost": True}
            }

            settings = voice_settings.get(emotion, voice_settings["calm"])

            # Enhanced text preprocessing for better speech
            processed_text = self._preprocess_text_for_speech(quote_text, emotion)

            try:
                # ElevenLabs API call with Grandpa Spuds voice
                voice_id = "pNInz6obpgDQGcFmaJgB"  # Grandpa Spuds Oxley voice ID
                url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

                headers = {
                    "Accept": "audio/mpeg",
                    "Content-Type": "application/json",
                    "xi-api-key": elevenlabs_key
                }

                data = {
                    "text": processed_text,
                    "model_id": "eleven_monolingual_v1",
                    "voice_settings": settings
                }

                response = requests.post(url, json=data, headers=headers, timeout=30)

                if response.status_code == 200:
                    # Save audio file with metadata
                    timestamp = int(time.time())
                    audio_filename = f"temp/voiceover_{emotion}_{timestamp}.mp3"

                    os.makedirs("temp", exist_ok=True)
                    with open(audio_filename, 'wb') as f:
                        f.write(response.content)

                    # Calculate estimated duration
                    word_count = len(quote_text.split())
                    estimated_duration = (word_count * 0.6) / speed

                    return f"Generated voiceover: {audio_filename} | Duration: ~{estimated_duration:.1f}s | Emotion: {emotion}"
                else:
                    return f"ElevenLabs API error: {response.status_code} - {response.text}"

            except Exception as api_error:
                print(f"ElevenLabs API call failed: {api_error}")
                return self._create_placeholder_audio(quote_text, emotion, speed)

        except Exception as e:
            return f"Error generating voiceover: {str(e)}"

    def _preprocess_text_for_speech(self, text: str, emotion: str) -> str:
        """Enhance text for better speech synthesis with emotion-specific processing"""
        # Add pauses for dramatic effect
        text = text.replace('. ', '... ')
        text = text.replace(', ', ', ')
        text = text.replace(': ', ': ')
        text = text.replace(';', '; ')

        # Emotion-specific text modifications
        if emotion == "inspiring":
            # Add emphasis for inspiring words
            inspiring_words = ['achieve', 'believe', 'dream', 'hope', 'strength', 'courage']
            for word in inspiring_words:
                text = text.replace(word, f"*{word}*")
        elif emotion == "wise":
            # Add thoughtful pauses
            text = text.replace(' - ', ' ... ')
            text = text.replace(' and ', ' ... and ')
        elif emotion == "peaceful":
            # Slow down with extra pauses
            text = text.replace('.', '...')

        # Add emphasis markers for universal spiritual words
        emphasis_words = ['peace', 'wisdom', 'love', 'truth', 'mindfulness', 'meditation', 'enlightenment']
        for word in emphasis_words:
            if word in text.lower():
                text = text.replace(word, f"*{word}*")
                text = text.replace(word.capitalize(), f"*{word.capitalize()}*")

        return text

    def _create_placeholder_audio(self, text: str, emotion: str, speed: float) -> str:
        """Create placeholder audio info when API is not available"""
        timestamp = int(time.time())
        audio_filename = f"temp/voiceover_placeholder_{emotion}_{timestamp}.txt"

        os.makedirs("temp", exist_ok=True)
        with open(audio_filename, 'w', encoding='utf-8') as f:
            f.write(f"Voiceover Text: {text}\n")
            f.write(f"Emotion: {emotion}\n")
            f.write(f"Speed: {speed}x\n")
            f.write(f"Estimated Duration: {(len(text.split()) * 0.6) / speed:.1f} seconds\n")
            f.write("Voice: Grandpa Spuds Oxley style\n")
            f.write("Note: ElevenLabs API not configured - placeholder created")

        return f"Placeholder voiceover: {audio_filename}"


class PixabayMusicInput(BaseModel):
    quote_mood: str = Field(description="Mood of the quote for music selection")
    duration: int = Field(default=30, description="Required music duration in seconds")
    genre: str = Field(default="ambient", description="Music genre (ambient, meditation, soft, instrumental)")

class PixabayMusicTool(BaseTool):
    name: str = "Pixabay Music Curator"
    description: str = "Sources and downloads royalty-free background music from Pixabay optimized for spiritual content"
    args_schema: Type[BaseModel] = PixabayMusicInput

    def _run(self, quote_mood: str, duration: int = 30, genre: str = "ambient") -> str:
        try:
            # Get Pixabay API key
            pixabay_key = os.getenv('PIXABAY_API_KEY')

            # Music selection based on mood and genre
            search_terms = {
                "peaceful": ["meditation", "zen", "calm", "peaceful", "tranquil"],
                "inspiring": ["uplifting", "motivational", "inspiring", "hopeful", "positive"],
                "zen": ["zen", "meditation", "mindfulness", "spiritual", "buddhist"],
                "meditative": ["meditation", "ambient", "healing", "chakra", "spiritual"],
                "wise": ["contemplative", "thoughtful", "wisdom", "ancient", "mystical"]
            }

            genre_filters = {
                "ambient": "ambient meditation peaceful",
                "meditation": "meditation zen mindfulness",
                "soft": "soft gentle calm",
                "instrumental": "instrumental piano guitar",
                "nature": "nature sounds water birds"
            }

            # Combine mood and genre for search
            mood_terms = search_terms.get(quote_mood, search_terms["peaceful"])
            genre_term = genre_filters.get(genre, genre_filters["ambient"])

            search_query = f"{random.choice(mood_terms)} {genre_term}"

            if pixabay_key and pixabay_key != 'your_pixabay_api_key':
                try:
                    # Pixabay Music API call
                    url = "https://pixabay.com/api/music/"
                    params = {
                        "key": pixabay_key,
                        "q": search_query,
                        "audio_type": "music",
                        "duration": "medium" if duration <= 60 else "long",
                        "per_page": 10,
                        "safesearch": "true"
                    }

                    response = requests.get(url, params=params, timeout=15)

                    if response.status_code == 200:
                        data = response.json()

                        if data.get('hits'):
                            # Select best matching track
                            track = self._select_best_track(data['hits'], quote_mood, duration)

                            if track:
                                # Download the track
                                download_result = self._download_music_track(track, quote_mood)
                                return download_result

                        return "No suitable music tracks found on Pixabay"
                    else:
                        return f"Pixabay API error: {response.status_code}"

                except Exception as api_error:
                    print(f"Pixabay API call failed: {api_error}")
                    return self._create_placeholder_music(quote_mood, duration, genre)
            else:
                return self._create_placeholder_music(quote_mood, duration, genre)

        except Exception as e:
            return f"Error curating music: {str(e)}"

    def _select_best_track(self, tracks: list, mood: str, target_duration: int) -> dict:
        """Select the best matching track based on mood and duration"""
        try:
            # Score tracks based on various factors
            scored_tracks = []

            for track in tracks:
                score = 0

                # Duration matching (prefer tracks close to target duration)
                track_duration = track.get('duration', 0)
                duration_diff = abs(track_duration - target_duration)
                if duration_diff <= 10:
                    score += 30
                elif duration_diff <= 30:
                    score += 20
                elif duration_diff <= 60:
                    score += 10

                # Tags matching
                tags = track.get('tags', '').lower()
                mood_keywords = {
                    "peaceful": ["peaceful", "calm", "zen", "meditation", "tranquil"],
                    "inspiring": ["uplifting", "motivational", "inspiring", "positive", "hopeful"],
                    "zen": ["zen", "meditation", "buddhist", "spiritual", "mindfulness"],
                    "meditative": ["meditation", "healing", "chakra", "spiritual", "ambient"],
                    "wise": ["contemplative", "wisdom", "ancient", "mystical", "thoughtful"]
                }

                keywords = mood_keywords.get(mood, mood_keywords["peaceful"])
                for keyword in keywords:
                    if keyword in tags:
                        score += 15

                # Quality indicators
                if track.get('downloads', 0) > 1000:
                    score += 10
                if track.get('likes', 0) > 100:
                    score += 5

                scored_tracks.append((score, track))

            # Return highest scoring track
            if scored_tracks:
                scored_tracks.sort(key=lambda x: x[0], reverse=True)
                return scored_tracks[0][1]

            return None

        except Exception as e:
            print(f"Error selecting track: {e}")
            return tracks[0] if tracks else None

    def _download_music_track(self, track: dict, mood: str) -> str:
        """Download the selected music track"""
        try:
            download_url = track.get('url')
            if not download_url:
                return "No download URL available for selected track"

            # Create filename
            timestamp = int(time.time())
            track_name = track.get('tags', 'music').replace(' ', '_')[:30]
            filename = f"temp/music_{mood}_{track_name}_{timestamp}.mp3"

            # Download the file
            response = requests.get(download_url, timeout=60)

            if response.status_code == 200:
                os.makedirs("temp", exist_ok=True)
                with open(filename, 'wb') as f:
                    f.write(response.content)

                # Get track info
                duration = track.get('duration', 0)
                tags = track.get('tags', 'Unknown')

                return f"Downloaded music: {filename} | Duration: {duration}s | Tags: {tags}"
            else:
                return f"Failed to download track: HTTP {response.status_code}"

        except Exception as e:
            return f"Error downloading track: {str(e)}"

    def _create_placeholder_music(self, mood: str, duration: int, genre: str) -> str:
        """Create placeholder music info when API is not available"""
        timestamp = int(time.time())
        music_filename = f"temp/music_placeholder_{mood}_{timestamp}.txt"

        # Curated music recommendations
        music_recommendations = {
            "peaceful": ["Calm Waters", "Zen Garden", "Peaceful Mind", "Tranquil Moments"],
            "inspiring": ["Rising Sun", "Mountain Peak", "Inner Strength", "New Horizons"],
            "zen": ["Meditation Bell", "Bamboo Forest", "Temple Silence", "Buddha's Garden"],
            "meditative": ["Chakra Healing", "Om Chanting", "Crystal Bowls", "Sacred Space"],
            "wise": ["Ancient Wisdom", "Elder's Tale", "Timeless Truth", "Sage's Journey"]
        }

        recommendations = music_recommendations.get(mood, music_recommendations["peaceful"])
        selected_track = random.choice(recommendations)

        os.makedirs("temp", exist_ok=True)
        with open(music_filename, 'w', encoding='utf-8') as f:
            f.write(f"Music Track: {selected_track}\n")
            f.write(f"Mood: {mood}\n")
            f.write(f"Genre: {genre}\n")
            f.write(f"Duration: {duration} seconds\n")
            f.write(f"Style: Royalty-free spiritual/ambient music\n")
            f.write("Note: Pixabay API not configured - placeholder created\n")
            f.write("Recommended: Search for similar tracks on Pixabay, Freesound, or YouTube Audio Library")

        return f"Placeholder music: {music_filename} | Recommended: {selected_track}"
