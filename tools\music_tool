from bs4 import BeautifulSoup
import requests

class PixabayMusicTool:
    def run(self, keywords="calm meditation ambient"):
        url = f"https://pixabay.com/music/search/{keywords.replace(' ', '%20')}/"
        headers = {"User-Agent": "Mozilla/5.0"}

        response = requests.get(url, headers=headers)
        soup = BeautifulSoup(response.text, "html.parser")
        music_links = soup.select("a[download]")

        if not music_links:
            return "No tracks found. Try a different keyword."

        top_track = music_links[0].get("href")
        full_link = "https://pixabay.com" + top_track
        return full_link
