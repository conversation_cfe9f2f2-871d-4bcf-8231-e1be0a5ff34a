class MidJourneyPromptTool:
    def run(self, theme: str = "Buddha meditation", style: str = "sunrise, cinematic, silhouette"):
        prompt = (
            f"A peaceful Buddha meditating on a mountaintop at golden hour, "
            f"{theme}, {style}, soft lighting, depth of field, atmospheric haze, "
            f"portrait, trending on artstation, 9:16 aspect ratio"
        )
        return prompt
