from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
from moviepy.editor import *
import os
import time
import random

class VideoCreatorInput(BaseModel):
    quote: str = Field(description="Quote text to create video for")
    duration: int = Field(default=10, description="Video duration in seconds")

class VideoCreatorTool(BaseTool):
    name: str = "Video Creator Tool"
    description: str = "Creates video content with quote text and background music"
    args_schema: Type[BaseModel] = VideoCreatorInput

    def _run(self, quote: str, duration: int = 10) -> str:
        try:
            # Ensure directories exist
            os.makedirs("temp", exist_ok=True)
            os.makedirs("assets/backgrounds", exist_ok=True)
            os.makedirs("assets/chinese_flute_sounds", exist_ok=True)

            # Create a simple colored background if no image available
            background_clip = ColorClip(size=(1080, 1920), color=(139, 69, 19), duration=duration)

            # Add text overlay
            txt_clip = TextClip(
                quote,
                fontsize=50,
                color='white',
                font='Arial-Bold',
                size=(900, None),
                method='caption'
            ).set_position('center').set_duration(duration)

            # Composite video
            video = CompositeVideoClip([background_clip, txt_clip])

            # Save video
            output_path = f"temp/video_{int(time.time())}.mp4"
            video.write_videofile(output_path, fps=24, verbose=False, logger=None)

            return output_path
        except Exception as e:
            return f"Error creating video: {str(e)}"