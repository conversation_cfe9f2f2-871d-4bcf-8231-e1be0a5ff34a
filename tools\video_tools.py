from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
from moviepy.editor import *
import cv2
import numpy as np
import os
import time

class VideoCreationInput(BaseModel):
    quote_text: str = Field(description="Quote text to display in video")
    image_path: str = Field(default="buddha.jpg", description="Path to background image")
    audio_path: str = Field(default="output_voice.mp3", description="Path to voiceover audio")
    music_path: str = Field(default="music.mp3", description="Path to background music")
    output_path: str = Field(default="", description="Output video path (auto-generated if empty)")

class VideoCreatorTool(BaseTool):
    name: str = "Video Creator"
    description: str = "Creates cinematic videos with background images, voiceover, and music for Instagram Stories"
    args_schema: Type[BaseModel] = VideoCreationInput

    def _run(self, quote_text: str, image_path: str = "buddha.jpg", audio_path: str = "output_voice.mp3",
             music_path: str = "music.mp3", output_path: str = "") -> str:
        try:
            # Generate output path if not provided
            if not output_path:
                timestamp = int(time.time())
                output_path = f"temp/video_{timestamp}.mp4"

            # Ensure temp directory exists
            os.makedirs("temp", exist_ok=True)

            # Check if input files exist
            if not os.path.exists(image_path):
                return f"Error: Image file not found: {image_path}"

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return f"Error: Could not load image: {image_path}"

            height, width, _ = image.shape
            new_width = 1080
            new_height = 1920
            image = cv2.resize(image, (new_width, new_height))

            # Create fade-in clip from image
            image_clip = ImageClip(image).set_duration(6).fadein(1).fadeout(1)

            # Load audio files if they exist
            audio_clips = []

            if os.path.exists(audio_path):
                narration = AudioFileClip(audio_path)
                audio_clips.append(narration.set_start(0))
                video_duration = narration.duration + 1
            else:
                video_duration = 6  # Default duration

            if os.path.exists(music_path):
                background_music = AudioFileClip(music_path).volumex(0.3)
                audio_clips.append(background_music.set_start(0))

            # Mix audio if available
            if audio_clips:
                final_audio = CompositeAudioClip(audio_clips)
                final_video = image_clip.set_audio(final_audio).set_duration(video_duration)
            else:
                final_video = image_clip.set_duration(video_duration)

            # Export video
            final_video.write_videofile(output_path, fps=24, verbose=False, logger=None)

            return f"Video created successfully: {output_path}"

        except Exception as e:
            return f"Error creating video: {str(e)}"

# Legacy class for backward compatibility
class CinematicVideoTool:
    def run(self, image_path="buddha.jpg", audio_path="output_voice.mp3", music_path="music.mp3", output="final_video.mp4"):
        tool = VideoCreatorTool()
        return tool._run("", image_path, audio_path, music_path, output)
