from moviepy.editor import *
import cv2
import numpy as np

class CinematicVideoTool:
    def run(self, image_path="buddha.jpg", audio_path="output_voice.mp3", music_path="music.mp3", output="final_video.mp4"):
        # Load image
        image = cv2.imread(image_path)
        height, width, _ = image.shape
        new_width = 1080
        new_height = 1920
        image = cv2.resize(image, (new_width, new_height))

        # Create fade-in clip from image
        image_clip = ImageClip(image).set_duration(6).fadein(1).fadeout(1)

        # Load audio
        narration = AudioFileClip(audio_path)
        background_music = AudioFileClip(music_path).volumex(0.3)

        # Mix audio
        final_audio = CompositeAudioClip([background_music.set_start(0), narration.set_start(0)])

        # Set audio and export
        final_video = image_clip.set_audio(final_audio).set_duration(narration.duration + 1)
        final_video.write_videofile(output, fps=24)

        return output
