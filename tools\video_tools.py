from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field
import os
import time
import random
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# Import MoviePy components
try:
    from moviepy.editor import VideoClip, ColorClip, CompositeVideoClip
    from moviepy.video.io.VideoFileClip import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError as e:
    print(f"MoviePy import error: {e}")
    MOVIEPY_AVAILABLE = False

class VideoCreatorInput(BaseModel):
    quote: str = Field(description="Quote text to create video for")
    duration: int = Field(default=10, description="Video duration in seconds")

class VideoCreatorTool(BaseTool):
    name: str = "Video Creator Tool"
    description: str = "Creates video content with quote text and background music"
    args_schema: Type[BaseModel] = VideoCreatorInput

    def _run(self, quote: str, duration: int = 10) -> str:
        try:
            # Ensure directories exist
            os.makedirs("temp", exist_ok=True)
            os.makedirs("assets/backgrounds", exist_ok=True)
            os.makedirs("assets/chinese_flute_sounds", exist_ok=True)

            if not MOVIEPY_AVAILABLE:
                # Fallback: save quote as text file
                quote_file = f"temp/quote_{int(time.time())}.txt"
                with open(quote_file, 'w', encoding='utf-8') as f:
                    f.write(f"Quote: {quote}\nDuration: {duration} seconds\nTimestamp: {time.time()}")
                return f"MoviePy not available. Quote saved as text: {quote_file}"

            # Create video using PIL for text rendering (no ImageMagick required)
            try:
                # Create text image using PIL
                text_image_path = self._create_text_image(quote)

                if text_image_path:
                    # Create video with text image
                    video = self._create_video_with_text_image(text_image_path, duration)
                else:
                    # Fallback: create simple colored background
                    video = ColorClip(size=(1080, 1920), color=(139, 69, 19), duration=duration)

                # Save video
                output_path = f"temp/video_{int(time.time())}.mp4"
                video.write_videofile(output_path, fps=24, verbose=False, logger=None)

                # Clean up temporary text image
                if text_image_path and os.path.exists(text_image_path):
                    os.remove(text_image_path)

                return output_path

            except Exception as video_error:
                print(f"Video creation failed: {video_error}")

                # Ultimate fallback: save quote as text file
                quote_file = f"temp/quote_{int(time.time())}.txt"
                with open(quote_file, 'w', encoding='utf-8') as f:
                    f.write(f"Quote: {quote}\nDuration: {duration} seconds\nTimestamp: {time.time()}")

                return f"Video creation failed. Quote saved as text: {quote_file}"

        except Exception as e:
            return f"Error in video creation process: {str(e)}"

    def _create_text_image(self, quote: str) -> str:
        """Create an image with text using PIL (no ImageMagick required)"""
        try:
            # Image dimensions (Instagram Story format)
            width, height = 1080, 1920

            # Create image with gradient background
            img = Image.new('RGB', (width, height), color=(139, 69, 19))
            draw = ImageDraw.Draw(img)

            # Try to load a font
            try:
                # Try to use a system font
                font_size = 60
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                try:
                    # Fallback to default font
                    font = ImageFont.load_default()
                except:
                    font = None

            if font:
                # Word wrap the text
                words = quote.split()
                lines = []
                current_line = []

                for word in words:
                    test_line = ' '.join(current_line + [word])
                    bbox = draw.textbbox((0, 0), test_line, font=font)
                    text_width = bbox[2] - bbox[0]

                    if text_width <= width - 100:  # 50px margin on each side
                        current_line.append(word)
                    else:
                        if current_line:
                            lines.append(' '.join(current_line))
                            current_line = [word]
                        else:
                            lines.append(word)

                if current_line:
                    lines.append(' '.join(current_line))

                # Calculate total text height
                line_height = 80
                total_text_height = len(lines) * line_height
                start_y = (height - total_text_height) // 2

                # Draw each line
                for i, line in enumerate(lines):
                    bbox = draw.textbbox((0, 0), line, font=font)
                    text_width = bbox[2] - bbox[0]
                    x = (width - text_width) // 2
                    y = start_y + i * line_height

                    # Draw text with shadow
                    draw.text((x + 2, y + 2), line, font=font, fill=(0, 0, 0))  # Shadow
                    draw.text((x, y), line, font=font, fill=(255, 255, 255))  # Main text

            # Save image
            image_path = f"temp/text_image_{int(time.time())}.png"
            img.save(image_path)
            return image_path

        except Exception as e:
            print(f"Error creating text image: {e}")
            return None

    def _create_video_with_text_image(self, text_image_path: str, duration: int):
        """Create video using the text image"""
        try:
            # Load the text image
            from moviepy.editor import ImageClip

            # Create video clip from image
            img_clip = ImageClip(text_image_path, duration=duration)

            return img_clip

        except Exception as e:
            print(f"Error creating video from image: {e}")
            # Fallback to colored background
            return ColorClip(size=(1080, 1920), color=(139, 69, 19), duration=duration)