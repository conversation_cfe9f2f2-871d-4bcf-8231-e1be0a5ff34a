#!/usr/bin/env python3
"""
CrewAI Configuration Manager with LLM Fallback Support
Handles dynamic reconfiguration of CrewAI agents with different LLM providers
"""

import os
import logging
from typing import Optional, Dict, Any
from utils.llm_fallback_manager import LLMFallbackManager, LLMProvider

try:
    from crewai import Agent, Task, Crew
    from langchain_openai import ChatOpenAI
    CREWAI_AVAILABLE = True
except ImportError:
    CREWAI_AVAILABLE = False
    print("Warning: CrewAI not available")

class CrewAIConfigManager:
    """Manages CrewAI configuration with LLM fallback support"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.llm_manager = LLMFallbackManager(logger)
        self.current_llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize the current LLM instance"""
        try:
            self.current_llm = self.llm_manager.get_current_llm()
            provider = self.llm_manager.current_provider.value
            self.logger.info(f"🤖 Initialized CrewAI with {provider} LLM")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize LLM: {str(e)}")
            self.current_llm = None
    
    def get_llm_for_agent(self) -> Optional[Any]:
        """Get the current LLM instance for CrewAI agents"""
        if not self.current_llm:
            self._initialize_llm()
        return self.current_llm
    
    def reconfigure_agents_with_fallback(self, agents: list) -> bool:
        """Reconfigure existing agents with fallback LLM"""
        try:
            if not self.llm_manager.switch_to_fallback():
                return False
            
            # Get new LLM instance
            new_llm = self.llm_manager.get_current_llm()
            if not new_llm:
                return False
            
            # Update all agents with new LLM
            updated_count = 0
            for agent in agents:
                if hasattr(agent, 'llm'):
                    agent.llm = new_llm
                    updated_count += 1
            
            self.current_llm = new_llm
            provider = self.llm_manager.current_provider.value
            self.logger.info(f"🔄 Reconfigured {updated_count} agents with {provider} LLM")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to reconfigure agents: {str(e)}")
            return False
    
    def create_agent_with_current_llm(self, role: str, goal: str, backstory: str, 
                                    tools: list = None, **kwargs) -> Optional[Agent]:
        """Create a new agent with the current LLM"""
        try:
            llm = self.get_llm_for_agent()
            if not llm:
                raise ValueError("No LLM available")
            
            # Set default parameters optimized for rate limiting
            agent_params = {
                'role': role,
                'goal': goal,
                'backstory': backstory,
                'tools': tools or [],
                'llm': llm,
                'verbose': kwargs.get('verbose', False),
                'max_iter': kwargs.get('max_iter', 3),
                'memory': kwargs.get('memory', False),
                'allow_delegation': kwargs.get('allow_delegation', False)
            }
            
            # Add any additional parameters
            for key, value in kwargs.items():
                if key not in agent_params:
                    agent_params[key] = value
            
            agent = Agent(**agent_params)
            provider = self.llm_manager.current_provider.value
            self.logger.debug(f"✅ Created agent '{role}' with {provider} LLM")
            return agent
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create agent '{role}': {str(e)}")
            return None
    
    def create_crew_with_fallback_support(self, agents: list, tasks: list, **kwargs) -> Optional[Crew]:
        """Create a crew with fallback support configuration"""
        try:
            # Ensure all agents have the current LLM
            for agent in agents:
                if hasattr(agent, 'llm') and not agent.llm:
                    agent.llm = self.get_llm_for_agent()
            
            # Set crew parameters optimized for rate limiting
            crew_params = {
                'agents': agents,
                'tasks': tasks,
                'verbose': kwargs.get('verbose', False),
                'memory': kwargs.get('memory', False),
                'planning': kwargs.get('planning', False),
                'process': kwargs.get('process', None)
            }
            
            # Add any additional parameters
            for key, value in kwargs.items():
                if key not in crew_params:
                    crew_params[key] = value
            
            crew = Crew(**crew_params)
            provider = self.llm_manager.current_provider.value
            self.logger.info(f"✅ Created crew with {len(agents)} agents using {provider} LLM")
            return crew
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create crew: {str(e)}")
            return None
    
    def execute_with_fallback(self, crew: Crew, max_retries: int = 2) -> tuple[bool, Any]:
        """Execute crew with automatic fallback on rate limits"""
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                self.llm_manager.increment_request_count()
                provider = self.llm_manager.current_provider.value
                
                if attempt > 0:
                    self.logger.info(f"🔄 Retry attempt {attempt} with {provider}")
                else:
                    self.logger.info(f"🚀 Executing crew with {provider} LLM")
                
                result = crew.kickoff()
                
                self.logger.info(f"✅ Crew execution successful with {provider}")
                return True, result
                
            except Exception as e:
                last_error = e
                self.logger.warning(f"⚠️  Crew execution failed on attempt {attempt + 1}: {str(e)}")
                
                # Handle the error and attempt fallback
                fallback_attempted, message = self.llm_manager.handle_llm_error(e)
                
                if fallback_attempted and attempt < max_retries:
                    self.logger.info(f"🔄 {message}, retrying...")
                    # Reconfigure crew agents with new LLM
                    if not self.reconfigure_agents_with_fallback(crew.agents):
                        self.logger.error("❌ Failed to reconfigure agents for fallback")
                        break
                    continue
                else:
                    if attempt < max_retries:
                        self.logger.warning(f"⏳ No fallback available, waiting before retry...")
                        import time
                        time.sleep(30)  # Wait before retry
                    break
        
        # All attempts failed
        provider = self.llm_manager.current_provider.value
        self.logger.error(f"❌ All crew execution attempts failed with {provider}")
        return False, str(last_error) if last_error else "Unknown error"
    
    def get_current_provider_info(self) -> Dict[str, Any]:
        """Get information about current provider and usage"""
        return {
            "current_provider": self.llm_manager.current_provider.value,
            "llm_available": self.current_llm is not None,
            "usage_stats": self.llm_manager.get_usage_stats()
        }
    
    def reset_to_primary_provider(self):
        """Reset to primary provider (OpenAI)"""
        self.llm_manager.reset_to_primary()
        self._initialize_llm()
    
    def force_switch_provider(self) -> bool:
        """Force switch to fallback provider"""
        try:
            if self.llm_manager.switch_to_fallback():
                self._initialize_llm()
                return True
            return False
        except Exception as e:
            self.logger.error(f"❌ Failed to force switch provider: {str(e)}")
            return False
