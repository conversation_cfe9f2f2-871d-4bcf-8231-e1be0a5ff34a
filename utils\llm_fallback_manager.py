#!/usr/bin/env python3
"""
LLM Fallback Manager for <PERSON> Quotes Instagram Bot
Handles automatic switching between OpenAI and DeepSeek APIs when rate limits are encountered
"""

import os
import logging
import time
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from langchain_openai import Chat<PERSON>penAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    print("Warning: LangChain not available - LLM fallback will be limited")

class LLMProvider(Enum):
    """Available LLM providers"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"

@dataclass
class LLMConfig:
    """Configuration for LLM providers"""
    provider: LLMProvider
    api_key: str
    model_name: str
    base_url: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.7
    timeout: int = 30

class RateLimitError(Exception):
    """Custom exception for rate limit errors"""
    pass

class LLMFallbackManager:
    """Manages automatic fallback between LLM providers"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.current_provider = LLMProvider.OPENAI
        self.fallback_provider = LLMProvider.DEEPSEEK
        self.rate_limit_cooldown = 60  # seconds
        self.last_rate_limit_time = 0

        # Initialize configurations
        self.configs = self._initialize_configs()
        self.llm_instances = {}

        # Track usage statistics
        self.usage_stats = {
            LLMProvider.OPENAI: {"requests": 0, "errors": 0, "rate_limits": 0},
            LLMProvider.DEEPSEEK: {"requests": 0, "errors": 0, "rate_limits": 0}
        }

    def _initialize_configs(self) -> Dict[LLMProvider, LLMConfig]:
        """Initialize LLM provider configurations"""
        configs = {}

        # OpenAI Configuration
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "your_openai_api_key":
            configs[LLMProvider.OPENAI] = LLMConfig(
                provider=LLMProvider.OPENAI,
                api_key=openai_key,
                model_name="gpt-4o-mini",
                max_tokens=1000,
                temperature=0.7,
                timeout=30
            )
            self.logger.info("[OK] OpenAI configuration loaded")
        else:
            self.logger.warning("[WARN] OpenAI API key not configured")

        # DeepSeek Configuration
        deepseek_key = os.getenv("DEEPSEEK_API_KEY")
        if deepseek_key and deepseek_key != "your_deepseek_api_key":
            configs[LLMProvider.DEEPSEEK] = LLMConfig(
                provider=LLMProvider.DEEPSEEK,
                api_key=deepseek_key,
                model_name="deepseek-chat",
                base_url="https://api.deepseek.com/v1",
                max_tokens=1000,
                temperature=0.7,
                timeout=30
            )
            self.logger.info("[OK] DeepSeek configuration loaded")
        else:
            self.logger.warning("[WARN] DeepSeek API key not configured")

        return configs

    def _create_llm_instance(self, provider: LLMProvider):
        """Create LLM instance for the specified provider"""
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("LangChain not available for LLM creation")

        config = self.configs.get(provider)
        if not config:
            raise ValueError(f"No configuration found for provider: {provider.value}")

        try:
            if provider == LLMProvider.OPENAI:
                llm = ChatOpenAI(
                    api_key=config.api_key,
                    model=config.model_name,
                    max_tokens=config.max_tokens,
                    temperature=config.temperature,
                    timeout=config.timeout
                )
            elif provider == LLMProvider.DEEPSEEK:
                # DeepSeek uses OpenAI-compatible API
                llm = ChatOpenAI(
                    api_key=config.api_key,
                    model=config.model_name,
                    base_url=config.base_url,
                    max_tokens=config.max_tokens,
                    temperature=config.temperature,
                    timeout=config.timeout
                )
            else:
                raise ValueError(f"Unsupported provider: {provider.value}")

            self.llm_instances[provider] = llm
            self.logger.info(f"[OK] Created {provider.value} LLM instance")
            return llm

        except Exception as e:
            self.logger.error(f"❌ Failed to create {provider.value} LLM instance: {str(e)}")
            raise

    def get_current_llm(self):
        """Get the current active LLM instance"""
        if self.current_provider not in self.llm_instances:
            return self._create_llm_instance(self.current_provider)
        return self.llm_instances[self.current_provider]

    def is_rate_limit_error(self, error: Exception) -> bool:
        """Check if the error is a rate limit error"""
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()

        # Check for common rate limit indicators
        rate_limit_indicators = [
            "rate limit",
            "rate_limit_exceeded",
            "429",
            "too many requests",
            "quota exceeded",
            "requests per minute"
        ]

        return any(indicator in error_str for indicator in rate_limit_indicators) or \
               "ratelimiterror" in error_type

    def should_attempt_fallback(self) -> bool:
        """Determine if fallback should be attempted"""
        # Check if fallback provider is available
        if self.fallback_provider not in self.configs:
            return False

        # Check cooldown period
        current_time = time.time()
        if current_time - self.last_rate_limit_time < self.rate_limit_cooldown:
            return False

        return True

    def switch_to_fallback(self) -> bool:
        """Switch to fallback provider"""
        if not self.should_attempt_fallback():
            return False

        try:
            old_provider = self.current_provider
            self.current_provider = self.fallback_provider
            self.fallback_provider = old_provider

            # Create new LLM instance
            self.get_current_llm()

            self.logger.info(f"🔄 Switched from {old_provider.value} to {self.current_provider.value}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to switch to fallback provider: {str(e)}")
            # Revert the switch
            self.fallback_provider = self.current_provider
            self.current_provider = old_provider
            return False

    def handle_llm_error(self, error: Exception) -> Tuple[bool, str]:
        """Handle LLM errors and attempt fallback if appropriate"""
        provider_name = self.current_provider.value
        self.usage_stats[self.current_provider]["errors"] += 1

        if self.is_rate_limit_error(error):
            self.usage_stats[self.current_provider]["rate_limits"] += 1
            self.last_rate_limit_time = time.time()

            self.logger.warning(f"⏰ Rate limit detected for {provider_name}: {str(error)}")

            if self.switch_to_fallback():
                return True, f"Switched to {self.current_provider.value} due to rate limits"
            else:
                return False, f"Rate limit hit for {provider_name}, no fallback available"
        else:
            self.logger.error(f"❌ LLM error for {provider_name}: {str(error)}")
            return False, f"LLM error: {str(error)}"

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics for all providers"""
        return {
            "current_provider": self.current_provider.value,
            "fallback_provider": self.fallback_provider.value,
            "stats": {provider.value: stats for provider, stats in self.usage_stats.items()},
            "last_rate_limit": self.last_rate_limit_time,
            "available_providers": list(self.configs.keys())
        }

    def reset_to_primary(self):
        """Reset to primary provider (OpenAI)"""
        if LLMProvider.OPENAI in self.configs:
            old_provider = self.current_provider
            self.current_provider = LLMProvider.OPENAI
            self.fallback_provider = LLMProvider.DEEPSEEK
            self.logger.info(f"🔄 Reset from {old_provider.value} to {self.current_provider.value}")

    def increment_request_count(self):
        """Increment request count for current provider"""
        self.usage_stats[self.current_provider]["requests"] += 1
